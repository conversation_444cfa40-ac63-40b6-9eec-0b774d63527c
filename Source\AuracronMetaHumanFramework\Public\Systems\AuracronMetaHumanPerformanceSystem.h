﻿// AURACRON MetaHuman Performance System - Advanced Performance Optimization for UE 5.6
// Handles MetaHuman performance monitoring, optimization, and resource management
// Author: Augment Agent
// Date: 2025-08-05
// Version: 2.0.0

#pragma once

#include "CoreMinimal.h"
#include "UObject/Object.h"
#include "Engine/Engine.h"
#include "HAL/PlatformMemory.h"
#include "Stats/Stats.h"
#include "ProfilingDebugging/CpuProfilerTrace.h"
#include "ProfilingDebugging/MemoryTrace.h"
#include "Trace/Trace.h"
#include "GenericPlatform/GenericPlatformMemoryStats.h"
#include "Async/AsyncWork.h"
#include "Containers/Queue.h"
#include "HAL/ThreadSafeBool.h"

#include "AuracronMetaHumanPerformanceSystem.generated.h"

DECLARE_LOG_CATEGORY_EXTERN(LogAuracronMetaHumanPerformance, Log, All);

// Forward declarations
class UAuracronMetaHumanFramework;

/**
 * Performance Monitoring Types
 */
UENUM(BlueprintType)
enum class EAuracronPerformanceMetric : uint8
{
    FrameRate           UMETA(DisplayName = "Frame Rate"),
    MemoryUsage         UMETA(DisplayName = "Memory Usage"),
    CPUUsage            UMETA(DisplayName = "CPU Usage"),
    GPUUsage            UMETA(DisplayName = "GPU Usage"),
    DrawCalls           UMETA(DisplayName = "Draw Calls"),
    Triangles           UMETA(DisplayName = "Triangle Count"),
    TextureMemory       UMETA(DisplayName = "Texture Memory"),
    AnimationCost       UMETA(DisplayName = "Animation Cost"),
    PhysicsCost         UMETA(DisplayName = "Physics Cost"),
    RenderingCost       UMETA(DisplayName = "Rendering Cost"),
    LoadingTime         UMETA(DisplayName = "Loading Time"),
    NetworkLatency      UMETA(DisplayName = "Network Latency")
};

/**
 * Performance Optimization Levels
 */
UENUM(BlueprintType)
enum class EAuracronOptimizationLevel : uint8
{
    None                UMETA(DisplayName = "No Optimization"),
    Conservative        UMETA(DisplayName = "Conservative"),
    Balanced            UMETA(DisplayName = "Balanced"),
    Aggressive          UMETA(DisplayName = "Aggressive"),
    Maximum             UMETA(DisplayName = "Maximum"),
    Custom              UMETA(DisplayName = "Custom")
};

/**
 * Performance Sample Data
 */
USTRUCT(BlueprintType)
struct AURACRONMETAHUMANFRAMEWORK_API FAuracronPerformanceSample
{
    GENERATED_BODY()

    /** Timestamp of the sample */
    UPROPERTY(BlueprintReadOnly, Category = "Sample")
    FDateTime Timestamp;

    /** Frame rate in FPS */
    UPROPERTY(BlueprintReadOnly, Category = "Sample")
    float FrameRate = 0.0f;

    /** Frame time in milliseconds */
    UPROPERTY(BlueprintReadOnly, Category = "Sample")
    float FrameTimeMS = 0.0f;

    /** Memory usage in MB */
    UPROPERTY(BlueprintReadOnly, Category = "Sample")
    float MemoryUsageMB = 0.0f;

    /** CPU usage percentage */
    UPROPERTY(BlueprintReadOnly, Category = "Sample")
    float CPUUsagePercent = 0.0f;

    /** GPU usage percentage */
    UPROPERTY(BlueprintReadOnly, Category = "Sample")
    float GPUUsagePercent = 0.0f;

    /** Draw call count */
    UPROPERTY(BlueprintReadOnly, Category = "Sample")
    int32 DrawCalls = 0;

    /** Triangle count */
    UPROPERTY(BlueprintReadOnly, Category = "Sample")
    int32 Triangles = 0;

    /** Texture memory usage in MB */
    UPROPERTY(BlueprintReadOnly, Category = "Sample")
    float TextureMemoryMB = 0.0f;

    FAuracronPerformanceSample()
    {
        Timestamp = FDateTime::Now();
    }
};

/**
 * Performance Statistics
 */
USTRUCT(BlueprintType)
struct AURACRONMETAHUMANFRAMEWORK_API FAuracronPerformanceStats
{
    GENERATED_BODY()

    /** Average frame rate */
    UPROPERTY(BlueprintReadOnly, Category = "Stats")
    float AverageFrameRate = 0.0f;

    /** Minimum frame rate */
    UPROPERTY(BlueprintReadOnly, Category = "Stats")
    float MinFrameRate = 0.0f;

    /** Maximum frame rate */
    UPROPERTY(BlueprintReadOnly, Category = "Stats")
    float MaxFrameRate = 0.0f;

    /** Average memory usage in MB */
    UPROPERTY(BlueprintReadOnly, Category = "Stats")
    float AverageMemoryUsageMB = 0.0f;

    /** Peak memory usage in MB */
    UPROPERTY(BlueprintReadOnly, Category = "Stats")
    float PeakMemoryUsageMB = 0.0f;

    /** Average CPU usage percentage */
    UPROPERTY(BlueprintReadOnly, Category = "Stats")
    float AverageCPUUsage = 0.0f;

    /** Peak CPU usage percentage */
    UPROPERTY(BlueprintReadOnly, Category = "Stats")
    float PeakCPUUsage = 0.0f;

    /** Average GPU usage percentage */
    UPROPERTY(BlueprintReadOnly, Category = "Stats")
    float AverageGPUUsage = 0.0f;

    /** Peak GPU usage percentage */
    UPROPERTY(BlueprintReadOnly, Category = "Stats")
    float PeakGPUUsage = 0.0f;

    /** Total samples collected */
    UPROPERTY(BlueprintReadOnly, Category = "Stats")
    int32 TotalSamples = 0;

    /** Monitoring duration in seconds */
    UPROPERTY(BlueprintReadOnly, Category = "Stats")
    float MonitoringDurationSeconds = 0.0f;

    FAuracronPerformanceStats()
    {
        // Default constructor
    }
};

/**
 * Optimization Configuration
 */
USTRUCT(BlueprintType)
struct AURACRONMETAHUMANFRAMEWORK_API FAuracronOptimizationConfig
{
    GENERATED_BODY()

    /** Optimization level */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Optimization")
    EAuracronOptimizationLevel OptimizationLevel = EAuracronOptimizationLevel::Balanced;

    /** Target frame rate */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Optimization", meta = (ClampMin = "30", ClampMax = "120"))
    int32 TargetFrameRate = 60;

    /** Maximum memory usage in MB */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Optimization", meta = (ClampMin = "512", ClampMax = "8192"))
    int32 MaxMemoryUsageMB = 2048;

    /** Enable LOD optimization */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Optimization")
    bool bEnableLODOptimization = true;

    /** Enable texture streaming optimization */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Optimization")
    bool bEnableTextureStreamingOptimization = true;

    /** Enable animation optimization */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Optimization")
    bool bEnableAnimationOptimization = true;

    /** Enable culling optimization */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Optimization")
    bool bEnableCullingOptimization = true;

    /** Enable GPU optimization */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Optimization")
    bool bEnableGPUOptimization = true;

    FAuracronOptimizationConfig()
    {
        // Default constructor
    }
};

DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FAuracronPerformanceAlert, const FString&, AlertMessage);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FAuracronOptimizationComplete, const FString&, OptimizationResult);

/**
 * AURACRON MetaHuman Performance System
 * Advanced performance monitoring and optimization system for UE 5.6
 */
UCLASS(BlueprintType, Blueprintable, Category = "AURACRON|MetaHuman|Performance")
class AURACRONMETAHUMANFRAMEWORK_API UAuracronMetaHumanPerformanceSystem : public UObject
{
    GENERATED_BODY()

public:
    UAuracronMetaHumanPerformanceSystem();
    virtual ~UAuracronMetaHumanPerformanceSystem();

    // === Core Performance Monitoring ===

    /**
     * Start performance monitoring
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|Performance|Monitoring", CallInEditor)
    bool StartPerformanceMonitoring(float SampleIntervalSeconds = 1.0f);

    /**
     * Stop performance monitoring
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|Performance|Monitoring", CallInEditor)
    void StopPerformanceMonitoring();

    /**
     * Get current performance sample
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|Performance|Monitoring", CallInEditor)
    FAuracronPerformanceSample GetCurrentPerformanceSample();

    /**
     * Get performance statistics
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|Performance|Monitoring", CallInEditor)
    FAuracronPerformanceStats GetPerformanceStatistics();

    /**
     * Reset performance statistics
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|Performance|Monitoring", CallInEditor)
    void ResetPerformanceStatistics();

    // === Performance Optimization ===

    /**
     * Apply performance optimization
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|Performance|Optimization", CallInEditor)
    bool ApplyPerformanceOptimization(const FAuracronOptimizationConfig& Config);

    /**
     * Optimize MetaHuman LODs
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|Performance|Optimization", CallInEditor)
    bool OptimizeMetaHumanLODs(AActor* MetaHumanActor, int32 TargetLODLevel);

    /**
     * Optimize texture streaming
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|Performance|Optimization", CallInEditor)
    bool OptimizeTextureStreaming(const TArray<UTexture*>& Textures);

    /**
     * Optimize animation performance
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|Performance|Optimization", CallInEditor)
    bool OptimizeAnimationPerformance(USkeletalMeshComponent* MeshComponent);

    // === Memory Management ===

    /**
     * Get memory usage breakdown
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|Performance|Memory", CallInEditor)
    TMap<FString, float> GetMemoryUsageBreakdown();

    /**
     * Force garbage collection
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|Performance|Memory", CallInEditor)
    void ForceGarbageCollection();

    /**
     * Optimize memory usage
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|Performance|Memory", CallInEditor)
    bool OptimizeMemoryUsage();

    /**
     * Set memory budget
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|Performance|Memory", CallInEditor)
    bool SetMemoryBudget(int32 BudgetMB);

    // === GPU Performance ===

    /**
     * Get GPU performance metrics
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|Performance|GPU", CallInEditor)
    TMap<FString, float> GetGPUPerformanceMetrics();

    /**
     * Optimize GPU performance
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|Performance|GPU", CallInEditor)
    bool OptimizeGPUPerformance();

    /**
     * Set GPU budget
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|Performance|GPU", CallInEditor)
    bool SetGPUBudget(float BudgetMS);

    // === Performance Profiling ===

    /**
     * Start CPU profiling
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|Performance|Profiling", CallInEditor)
    bool StartCPUProfiling();

    /**
     * Stop CPU profiling
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|Performance|Profiling", CallInEditor)
    FString StopCPUProfiling();

    /**
     * Start memory profiling
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|Performance|Profiling", CallInEditor)
    bool StartMemoryProfiling();

    /**
     * Stop memory profiling
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|Performance|Profiling", CallInEditor)
    FString StopMemoryProfiling();

    // === Performance Alerts ===

    /**
     * Set performance alert thresholds
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|Performance|Alerts", CallInEditor)
    void SetPerformanceAlertThresholds(float MinFrameRate, float MaxMemoryMB, float MaxCPUPercent);

    /**
     * Enable performance alerts
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|Performance|Alerts", CallInEditor)
    void EnablePerformanceAlerts(bool bEnable);

    // === Utility Functions ===

    /**
     * Initialize performance system
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|Performance|Utility", CallInEditor)
    bool Initialize(UAuracronMetaHumanFramework* InFramework);

    /**
     * Shutdown performance system
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|Performance|Utility", CallInEditor)
    void Shutdown();

    /**
     * Check if performance system is initialized
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|Performance|Utility", CallInEditor)
    bool IsInitialized() const;

    /**
     * Get system performance capabilities
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|Performance|Utility", CallInEditor)
    TMap<FString, FString> GetSystemPerformanceCapabilities();

public:
    // === Events ===

    /** Called when a performance alert is triggered */
    UPROPERTY(BlueprintAssignable, Category = "AURACRON MetaHuman|Performance|Events")
    FAuracronPerformanceAlert OnPerformanceAlert;

    /** Called when optimization is complete */
    UPROPERTY(BlueprintAssignable, Category = "AURACRON MetaHuman|Performance|Events")
    FAuracronOptimizationComplete OnOptimizationComplete;

protected:
    // === Internal State ===

    UPROPERTY()
    TObjectPtr<UAuracronMetaHumanFramework> OwnerFramework;

    UPROPERTY()
    bool bIsInitialized = false;

    UPROPERTY()
    bool bIsMonitoring = false;

    UPROPERTY()
    float SampleInterval = 1.0f;

    UPROPERTY()
    FString LastErrorMessage;

    // Performance data
    TQueue<FAuracronPerformanceSample> PerformanceSamples;
    FAuracronPerformanceStats CurrentStats;
    FCriticalSection PerformanceDataMutex;

    // Monitoring thread
    TSharedPtr<class FAuracronPerformanceMonitoringTask> MonitoringTask;
    FThreadSafeBool bStopMonitoring;

    // Alert thresholds
    float AlertMinFrameRate = 30.0f;
    float AlertMaxMemoryMB = 2048.0f;
    float AlertMaxCPUPercent = 80.0f;
    bool bAlertsEnabled = false;

    // Profiling state
    bool bCPUProfilingActive = false;
    bool bMemoryProfilingActive = false;
    FDateTime ProfilingStartTime;

private:
    // === Internal Methods ===

    void UpdatePerformanceStatistics();
    void CheckPerformanceAlerts(const FAuracronPerformanceSample& Sample);
    FAuracronPerformanceSample CollectPerformanceSample();
    bool ApplyLODOptimization(const FAuracronOptimizationConfig& Config);
    bool ApplyTextureOptimization(const FAuracronOptimizationConfig& Config);
    bool ApplyAnimationOptimization(const FAuracronOptimizationConfig& Config);
    bool ApplyCullingOptimization(const FAuracronOptimizationConfig& Config);
    bool ApplyGPUOptimization(const FAuracronOptimizationConfig& Config);
    void StartMonitoringThread();
    void StopMonitoringThread();
};

