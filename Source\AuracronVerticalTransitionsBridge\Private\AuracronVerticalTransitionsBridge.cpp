// AURACRON - Implementação do Bridge C++ para Sistema de Transições Verticais
// Integração com Unreal Engine 5.6 APIs
// Autor: Augment Agent
// Data: 2025-08-03
// Versão: 1.0.0

#include "AuracronVerticalTransitionsBridge.h"
#include "Engine/Engine.h"
#include "Engine/World.h"
#include "NavigationSystem.h"
#include "Components/StaticMeshComponent.h"
#include "Components/BoxComponent.h"
#include "Particles/ParticleSystemComponent.h"
#include "Components/AudioComponent.h"
#include "GameFramework/Character.h"
#include "GameFramework/PlayerController.h"
#include "Kismet/GameplayStatics.h"
#include "TimerManager.h"
#include "DrawDebugHelpers.h"
#include "Async/Async.h"
#include "HAL/ThreadSafeBool.h"

UAuracronVerticalTransitionsBridge::UAuracronVerticalTransitionsBridge()
{
    PrimaryComponentTick.bCanEverTick = true;
    PrimaryComponentTick.TickInterval = 0.1f; // 10 FPS para otimização

    // Configurações padrão
    bUse3DNavigation = true;
    TransitionDetectionRadius = 200.0f;
    MaxChannelingTime = 10.0f;
    bUseVisualEffects = true;
    bUseAudioEffects = true;
    GenerationSeed = 98765;
    
    // Inicializar configurações padrão de transições
    InitializeDefaultTransitionProperties();
}

void UAuracronVerticalTransitionsBridge::BeginPlay()
{
    Super::BeginPlay();

    UE_LOG(LogTemp, Warning, TEXT("AURACRON: Inicializando Sistema de Transições Verticais"));

    // Validar configuração
    if (!ValidateSystemConfiguration())
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Configuração do sistema inválida"));
        return;
    }

    // Inicializar sistema
    bSystemInitialized = InitializeTransitionSystem();
    
    if (bSystemInitialized)
    {
        UE_LOG(LogTemp, Log, TEXT("AURACRON: Sistema de Transições Verticais inicializado com sucesso"));
        
        // Construir rede de transições
        BuildTransitionNetwork();
        
        // Configurar timer de atualização
        if (GetWorld())
        {
            GetWorld()->GetTimerManager().SetTimer(UpdateTimer, this, 
                &UAuracronVerticalTransitionsBridge::UpdateTransitionSystem, 1.0f, true);
        }
    }
    else
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Falha ao inicializar Sistema de Transições Verticais"));
    }
}

void UAuracronVerticalTransitionsBridge::EndPlay(const EEndPlayReason::Type EndPlayReason)
{
    // Limpar timer
    if (GetWorld() && UpdateTimer.IsValid())
    {
        GetWorld()->GetTimerManager().ClearTimer(UpdateTimer);
    }
    
    // Cancelar todos os channelings
    for (auto& ChannelingPair : PlayersChanneling)
    {
        CancelTransitionChanneling(ChannelingPair.Key);
    }
    
    // Limpar rede de transições
    ClearTransitionNetwork();
    
    Super::EndPlay(EndPlayReason);
}

void UAuracronVerticalTransitionsBridge::TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction)
{
    Super::TickComponent(DeltaTime, TickType, ThisTickFunction);
    
    if (!bSystemInitialized)
    {
        return;
    }
    
    // Atualizar cooldowns
    UpdateTransitionCooldowns(DeltaTime);
    
    // Processar channelings ativos
    ProcessActiveChannelings(DeltaTime);
    
    // Detectar jogadores próximos a transições
    if (GetWorld() && GetWorld()->GetTimeSeconds() - LastPlayerDetectionTime > 0.5f)
    {
        DetectPlayersNearTransitions();
        LastPlayerDetectionTime = GetWorld()->GetTimeSeconds();
    }
}

// === Core Transition Management ===

bool UAuracronVerticalTransitionsBridge::CreateTransitionPoint(const FTransitionPointData& TransitionData)
{
    if (TransitionData.TransitionID.IsEmpty())
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: ID de transição não pode estar vazio"));
        return false;
    }
    
    FScopeLock Lock(&TransitionMutex);
    
    // Verificar se já existe
    for (const FTransitionPointData& ExistingTransition : TransitionNetwork.TransitionPoints)
    {
        if (ExistingTransition.TransitionID == TransitionData.TransitionID)
        {
            UE_LOG(LogTemp, Warning, TEXT("AURACRON: Transição %s já existe"), *TransitionData.TransitionID);
            return false;
        }
    }
    
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Criando ponto de transição %s"), *TransitionData.TransitionID);
    
    // Adicionar à rede
    TransitionNetwork.TransitionPoints.Add(TransitionData);
    TransitionNetwork.TotalTransitions++;
    
    // Criar componente visual se especificado
    if (TransitionData.TransitionMesh.IsValid())
    {
        CreateTransitionVisualComponent(TransitionData);
    }
    
    // Configurar efeitos
    if (bUseVisualEffects)
    {
        SetupVisualEffects(TransitionData.TransitionID);
    }
    
    if (bUseAudioEffects)
    {
        SetupAudioEffects(TransitionData.TransitionID);
    }
    
    // Atualizar navegação se necessário
    if (bUse3DNavigation)
    {
        UpdateNavigationForTransition(TransitionData);
    }
    
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Ponto de transição %s criado com sucesso"), *TransitionData.TransitionID);
    return true;
}

bool UAuracronVerticalTransitionsBridge::RemoveTransitionPoint(const FString& TransitionID)
{
    if (TransitionID.IsEmpty())
    {
        return false;
    }
    
    FScopeLock Lock(&TransitionMutex);
    
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Removendo ponto de transição %s"), *TransitionID);
    
    // Encontrar e remover da rede
    int32 RemovedCount = TransitionNetwork.TransitionPoints.RemoveAll([&TransitionID](const FTransitionPointData& Transition)
    {
        return Transition.TransitionID == TransitionID;
    });
    
    if (RemovedCount > 0)
    {
        TransitionNetwork.TotalTransitions -= RemovedCount;
        
        // Remover componentes visuais
        if (UStaticMeshComponent* Component = TransitionComponents.FindRef(TransitionID))
        {
            Component->DestroyComponent();
            TransitionComponents.Remove(TransitionID);
        }
        
        // Remover componentes de efeitos
        if (UParticleSystemComponent* EffectComponent = EffectComponents.FindRef(TransitionID))
        {
            EffectComponent->DestroyComponent();
            EffectComponents.Remove(TransitionID);
        }
        
        // Remover componentes de áudio
        if (UAudioComponent* AudioComponent = AudioComponents.FindRef(TransitionID))
        {
            AudioComponent->Stop();
            AudioComponent->DestroyComponent();
            AudioComponents.Remove(TransitionID);
        }
        
        // Remover cooldown
        TransitionCooldowns.Remove(TransitionID);
        
        // Cancelar channeling se ativo
        for (auto It = PlayersChanneling.CreateIterator(); It; ++It)
        {
            if (It.Value() == TransitionID)
            {
                CancelTransitionChanneling(It.Key());
                It.RemoveCurrent();
            }
        }
        
        UE_LOG(LogTemp, Log, TEXT("AURACRON: Ponto de transição %s removido"), *TransitionID);
        return true;
    }
    
    UE_LOG(LogTemp, Warning, TEXT("AURACRON: Ponto de transição %s não encontrado"), *TransitionID);
    return false;
}

bool UAuracronVerticalTransitionsBridge::ActivateTransition(const FString& TransitionID, APawn* Player)
{
    if (!Player || TransitionID.IsEmpty())
    {
        return false;
    }
    
    // Encontrar dados da transição
    FTransitionPointData* TransitionData = FindTransitionData(TransitionID);
    if (!TransitionData)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Transição %s não encontrada"), *TransitionID);
        return false;
    }
    
    // Verificar se está ativa
    if (!TransitionData->bIsActive)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Transição %s está desativada"), *TransitionID);
        return false;
    }
    
    // Verificar cooldown
    if (IsTransitionOnCooldown(TransitionID))
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Transição %s está em cooldown"), *TransitionID);
        return false;
    }
    
    // Verificar se jogador pode usar
    if (!CanPlayerUseTransition(TransitionID, Player))
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Jogador não pode usar transição %s"), *TransitionID);
        return false;
    }
    
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Ativando transição %s para jogador"), *TransitionID);
    
    // Verificar se requer channeling
    if (TransitionData->Properties.bRequiresChannel)
    {
        return StartTransitionChanneling(TransitionID, Player);
    }
    else
    {
        return ExecuteTransition(TransitionID, Player);
    }
}

bool UAuracronVerticalTransitionsBridge::ExecuteTransition(const FString& TransitionID, APawn* Player)
{
    if (!Player || TransitionID.IsEmpty())
    {
        return false;
    }
    
    FTransitionPointData* TransitionData = FindTransitionData(TransitionID);
    if (!TransitionData)
    {
        return false;
    }
    
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Executando transição %s"), *TransitionID);
    
    // Reproduzir efeito de ativação
    PlayTransitionEffect(TransitionID, true);
    
    // Calcular posição de destino
    FVector DestinationLocation = TransitionData->DestinationLocation;
    FRotator DestinationRotation = TransitionData->DestinationRotation;
    
    // Ajustar posição baseado no tipo de transição
    AdjustDestinationForTransitionType(TransitionData->TransitionType, DestinationLocation, DestinationRotation);
    
    // Executar transição baseado no tipo
    bool bSuccess = false;
    switch (TransitionData->TransitionType)
    {
        case EAuracronVerticalTransitionType::Portal:
            bSuccess = ExecuteTeleportTransition(Player, DestinationLocation, DestinationRotation);
            break;

        case EAuracronVerticalTransitionType::Elevator:
        case EAuracronVerticalTransitionType::FloatingPlatform:
            bSuccess = ExecuteElevatorTransition(Player, DestinationLocation, TransitionData->Properties.TransitionSpeed);
            break;
            
        case EAuracronVerticalTransitionType::JumpPad:
            bSuccess = ExecuteJumpPadTransition(Player, DestinationLocation, TransitionData->Properties.TransitionSpeed);
            break;

        case EAuracronVerticalTransitionType::WindCurrent:
            bSuccess = ExecuteWindCurrentTransition(Player, DestinationLocation, TransitionData->Properties.TransitionSpeed);
            break;

        case EAuracronVerticalTransitionType::Stairs:
        case EAuracronVerticalTransitionType::CaveEntrance:
            bSuccess = ExecuteWalkTransition(Player, DestinationLocation);
            break;
            
        default:
            bSuccess = ExecuteTeleportTransition(Player, DestinationLocation, DestinationRotation);
            break;
    }
    
    if (bSuccess)
    {
        // Aplicar cooldown
        TransitionCooldowns.Add(TransitionID, TransitionData->Properties.CooldownTime);
        
        // Reproduzir efeito de chegada
        PlayTransitionEffect(TransitionID, false);
        
        UE_LOG(LogTemp, Log, TEXT("AURACRON: Transição %s executada com sucesso"), *TransitionID);
    }
    else
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Falha ao executar transição %s"), *TransitionID);
    }
    
    return bSuccess;
}

// === Internal Helper Functions ===

void UAuracronVerticalTransitionsBridge::UpdateTransitionCooldowns(float DeltaTime)
{
    FScopeLock Lock(&TransitionMutex);
    
    // Atualizar cooldowns
    for (auto& CooldownPair : TransitionCooldowns)
    {
        if (CooldownPair.Value > 0.0f)
        {
            CooldownPair.Value -= DeltaTime;
            if (CooldownPair.Value <= 0.0f)
            {
                CooldownPair.Value = 0.0f;
                UE_LOG(LogTemp, VeryVerbose, TEXT("AURACRON: Cooldown da transição %s finalizado"), *CooldownPair.Key);
            }
        }
    }
}

void UAuracronVerticalTransitionsBridge::ProcessActiveChannelings(float DeltaTime)
{
    FScopeLock Lock(&TransitionMutex);
    
    TArray<APawn*> PlayersToRemove;
    
    // Processar channelings ativos
    for (auto& ChannelingPair : PlayersChanneling)
    {
        APawn* Player = ChannelingPair.Key;
        const FString& TransitionID = ChannelingPair.Value;
        
        if (!IsValid(Player))
        {
            PlayersToRemove.Add(Player);
            continue;
        }
        
        // Verificar se o jogador ainda está na área de transição
        // Implementação básica - pode ser expandida
        UE_LOG(LogTemp, VeryVerbose, TEXT("AURACRON: Processando channeling do jogador para transição %s"), *TransitionID);
    }
    
    // Remover jogadores inválidos
    for (APawn* Player : PlayersToRemove)
    {
        PlayersChanneling.Remove(Player);
    }
}

void UAuracronVerticalTransitionsBridge::DetectPlayersNearTransitions()
{
    if (!GetWorld())
    {
        return;
    }
    
    FScopeLock Lock(&TransitionMutex);
    
    // Obter todos os jogadores no mundo
    for (FConstPlayerControllerIterator Iterator = GetWorld()->GetPlayerControllerIterator(); Iterator; ++Iterator)
    {
        APlayerController* PC = Iterator->Get();
        if (!IsValid(PC) || !IsValid(PC->GetPawn()))
        {
            continue;
        }
        
        APawn* Player = PC->GetPawn();
        FVector PlayerLocation = Player->GetActorLocation();
        
        // Verificar proximidade com pontos de transição
        for (const auto& TransitionPair : TransitionPoints)
        {
            const FTransitionPointData& TransitionData = TransitionPair.Value;
            float Distance = FVector::Dist(PlayerLocation, TransitionData.Location);
            
            if (Distance <= TransitionData.TriggerRadius)
            {
                UE_LOG(LogTemp, VeryVerbose, TEXT("AURACRON: Jogador próximo à transição %s (distância: %.2f)"), *TransitionPair.Key, Distance);
                // Aqui pode ser adicionada lógica adicional para notificar o jogador
            }
        }
    }
}
