#include "HarmonyEngineSubsystem.h"
#include "AuracronHarmonyEngineBridge.h"
#include "EmotionalIntelligenceComponent.h"
#include "PositiveBehaviorPredictor.h"
#include "CommunityHealingManager.h"
#include "HarmonyRewardsSystem.h"
#include "RealTimeInterventionSystem.h"
#include "GameFramework/PlayerController.h"
#include "GameFramework/PlayerState.h"
#include "AbilitySystemComponent.h"
#include "Engine/World.h"
#include "TimerManager.h"
#include "Kismet/GameplayStatics.h"
#include "Engine/Engine.h"

DEFINE_LOG_CATEGORY(LogHarmonyEngine);

UHarmonyEngineSubsystem::UHarmonyEngineSubsystem()
{
    // Default configuration values
    ToxicityThreshold = 0.7f;
    InterventionCooldown = FTimespan::FromMinutes(5.0); // 5 minutes
    MaxConcurrentInterventions = 5;
    bEnableRealTimeMonitoring = true;
    bEnablePredictiveIntervention = true;
}

void UHarmonyEngineSubsystem::Initialize(FSubsystemCollectionBase& Collection)
{
    Super::Initialize(Collection);
    
    UE_LOG(LogHarmonyEngine, Log, TEXT("Initializing Harmony Engine Subsystem"));
    
    // Create core components
    EmotionalIntelligence = NewObject<UEmotionalIntelligenceComponent>(this);
    BehaviorPredictor = NewObject<UPositiveBehaviorPredictor>(this);
    HealingManager = NewObject<UCommunityHealingManager>(this);
    RewardsSystem = NewObject<UHarmonyRewardsSystem>(this);
    InterventionSystem = NewObject<URealTimeInterventionSystem>(this);
    
    // Initialize components
    if (EmotionalIntelligence)
    {
        EmotionalIntelligence->RegisterComponent();
    }
    
    // Setup timers for periodic analysis
    if (UWorld* World = GetWorld())
    {
        World->GetTimerManager().SetTimer(
            BehaviorAnalysisTimer,
            this,
            &UHarmonyEngineSubsystem::PerformPeriodicAnalysis,
            30.0f, // Every 30 seconds
            true
        );
        
        World->GetTimerManager().SetTimer(
            CommunityHealthTimer,
            this,
            &UHarmonyEngineSubsystem::UpdateCommunityHealth,
            60.0f, // Every minute
            true
        );
    }
    
    // Load persistent data
    LoadPlayerData();
    
    UE_LOG(LogHarmonyEngine, Log, TEXT("Harmony Engine Subsystem initialized successfully"));
}

void UHarmonyEngineSubsystem::Deinitialize()
{
    UE_LOG(LogHarmonyEngine, Log, TEXT("Deinitializing Harmony Engine Subsystem"));
    
    // Save data before shutdown
    SavePlayerData();
    
    // Clear timers
    if (UWorld* World = GetWorld())
    {
        World->GetTimerManager().ClearTimer(BehaviorAnalysisTimer);
        World->GetTimerManager().ClearTimer(CommunityHealthTimer);
    }
    
    // Cleanup components
    EmotionalIntelligence = nullptr;
    BehaviorPredictor = nullptr;
    HealingManager = nullptr;
    RewardsSystem = nullptr;
    InterventionSystem = nullptr;
    
    // Clear data structures
    PlayerBehaviorData.Empty();
    RegisteredPlayers.Empty();
    PlayerKindnessScores.Empty();
    
    Super::Deinitialize();
    
    UE_LOG(LogHarmonyEngine, Log, TEXT("Harmony Engine Subsystem deinitialized"));
}

bool UHarmonyEngineSubsystem::ShouldCreateSubsystem(UObject* Outer) const
{
    // Only create in game instances, not in editor
    return !IsRunningDedicatedServer() && !GIsEditor;
}

void UHarmonyEngineSubsystem::RegisterPlayer(APlayerController* PlayerController)
{
    if (!PlayerController || !PlayerController->GetPlayerState<APlayerState>())
    {
        UE_LOG(LogHarmonyEngine, Warning, TEXT("Cannot register invalid player controller"));
        return;
    }
    
    FString PlayerID = PlayerController->GetPlayerState<APlayerState>()->GetUniqueId().ToString();
    
    if (RegisteredPlayers.Contains(PlayerID))
    {
        UE_LOG(LogHarmonyEngine, Warning, TEXT("Player %s is already registered"), *PlayerID);
        return;
    }
    
    // Register player
    RegisteredPlayers.Add(PlayerID, PlayerController);
    
    // Initialize behavior data
    FPlayerBehaviorSnapshot InitialSnapshot;
    InitialSnapshot.PlayerID = PlayerID;
    InitialSnapshot.Timestamp = FDateTime::Now();
    PlayerBehaviorData.Add(PlayerID, InitialSnapshot);
    
    // Initialize kindness score
    PlayerKindnessScores.Add(PlayerID, 0.0f);
    
    // Start monitoring if enabled
    if (bEnableRealTimeMonitoring && EmotionalIntelligence)
    {
        EmotionalIntelligence->StartEmotionalMonitoring(PlayerID);
    }
    
    UE_LOG(LogHarmonyEngine, Log, TEXT("Player %s registered successfully"), *PlayerID);
    
    // Broadcast registration event
    OnBehaviorDetected.Broadcast(PlayerID, InitialSnapshot);
}

void UHarmonyEngineSubsystem::UnregisterPlayer(APlayerController* PlayerController)
{
    if (!PlayerController || !PlayerController->GetPlayerState<APlayerState>())
    {
        return;
    }
    
    FString PlayerID = PlayerController->GetPlayerState<APlayerState>()->GetUniqueId().ToString();
    
    if (!RegisteredPlayers.Contains(PlayerID))
    {
        return;
    }
    
    // Stop monitoring
    if (EmotionalIntelligence)
    {
        EmotionalIntelligence->StopEmotionalMonitoring(PlayerID);
    }
    
    // Save final data
    SavePlayerBehaviorData(PlayerID);
    
    // Remove from active tracking
    RegisteredPlayers.Remove(PlayerID);
    
    UE_LOG(LogHarmonyEngine, Log, TEXT("Player %s unregistered"), *PlayerID);
}

void UHarmonyEngineSubsystem::UpdatePlayerBehavior(const FString& PlayerID, const FPlayerBehaviorSnapshot& BehaviorData)
{
    if (!RegisteredPlayers.Contains(PlayerID))
    {
        UE_LOG(LogHarmonyEngine, Warning, TEXT("Attempting to update behavior for unregistered player: %s"), *PlayerID);
        return;
    }
    
    // Update behavior data
    PlayerBehaviorData.Add(PlayerID, BehaviorData);
    
    // Analyze for intervention needs
    if (bEnablePredictiveIntervention)
    {
        AnalyzeBehaviorForIntervention(PlayerID, BehaviorData);
    }
    
    // Update ML training data
    if (BehaviorPredictor)
    {
        BehaviorPredictor->AddTrainingData(BehaviorData);
    }
    
    // Broadcast behavior update
    OnBehaviorDetected.Broadcast(PlayerID, BehaviorData);
    
    UE_LOG(LogHarmonyEngine, VeryVerbose, TEXT("Updated behavior for player %s"), *PlayerID);
}

void UHarmonyEngineSubsystem::TriggerIntervention(const FString& PlayerID, EInterventionType InterventionType, const FString& Reason)
{
    if (!RegisteredPlayers.Contains(PlayerID))
    {
        UE_LOG(LogHarmonyEngine, Warning, TEXT("Cannot trigger intervention for unregistered player: %s"), *PlayerID);
        return;
    }
    
    // Check intervention cooldown
    if (IsPlayerOnInterventionCooldown(PlayerID))
    {
        UE_LOG(LogHarmonyEngine, Log, TEXT("Player %s is on intervention cooldown"), *PlayerID);
        return;
    }
    
    // Create intervention data
    FHarmonyInterventionData InterventionData;
    InterventionData.InterventionType = InterventionType;
    InterventionData.SuggestedAction = GenerateInterventionMessage(InterventionType, Reason);
    InterventionData.InterventionPriority = CalculateInterventionPriority(InterventionType);
    InterventionData.bRequiresImmediateAction = (InterventionType == EInterventionType::Emergency);
    
    // Execute intervention
    if (InterventionSystem)
    {
        InterventionSystem->ExecuteIntervention(PlayerID, InterventionData);
    }
    
    // Update cooldown
    UpdateInterventionCooldown(PlayerID);
    
    // Broadcast intervention event
    OnInterventionTriggered.Broadcast(PlayerID, InterventionData);
    
    UE_LOG(LogHarmonyEngine, Log, TEXT("Intervention triggered for player %s: %s"), *PlayerID, *Reason);
}

float UHarmonyEngineSubsystem::GetPlayerToxicityScore(const FString& PlayerID) const
{
    if (const FPlayerBehaviorSnapshot* BehaviorData = PlayerBehaviorData.Find(PlayerID))
    {
        return BehaviorData->ToxicityScore;
    }
    return 0.0f;
}

float UHarmonyEngineSubsystem::GetPlayerPositivityScore(const FString& PlayerID) const
{
    if (const FPlayerBehaviorSnapshot* BehaviorData = PlayerBehaviorData.Find(PlayerID))
    {
        return BehaviorData->PositivityScore;
    }
    return 0.0f;
}

EEmotionalState UHarmonyEngineSubsystem::GetPlayerEmotionalState(const FString& PlayerID) const
{
    if (const FPlayerBehaviorSnapshot* BehaviorData = PlayerBehaviorData.Find(PlayerID))
    {
        return BehaviorData->EmotionalState;
    }
    return EEmotionalState::Neutral;
}

bool UHarmonyEngineSubsystem::IsPlayerAtRisk(const FString& PlayerID) const
{
    const float ToxicityScore = GetPlayerToxicityScore(PlayerID);
    const EEmotionalState EmotionalState = GetPlayerEmotionalState(PlayerID);
    
    return (ToxicityScore > ToxicityThreshold) || 
           (EmotionalState == EEmotionalState::Angry) || 
           (EmotionalState == EEmotionalState::Frustrated);
}

void UHarmonyEngineSubsystem::AwardKindnessPoints(const FString& PlayerID, int32 Points, const FString& Reason)
{
    if (!RegisteredPlayers.Contains(PlayerID))
    {
        return;
    }
    
    // Update kindness score
    float& CurrentScore = PlayerKindnessScores.FindOrAdd(PlayerID);
    CurrentScore += Points;
    
    // Create reward data
    FKindnessReward Reward;
    Reward.KindnessPoints = Points;
    Reward.RewardDescription = Reason;
    Reward.ExperienceMultiplier = CalculateExperienceMultiplier(Points);
    
    // Process reward through rewards system
    if (RewardsSystem)
    {
        RewardsSystem->ProcessKindnessReward(PlayerID, Reward);
    }
    
    // Broadcast reward event
    OnKindnessReward.Broadcast(PlayerID, Reward);
    
    UE_LOG(LogHarmonyEngine, Log, TEXT("Awarded %d kindness points to player %s for: %s"), Points, *PlayerID, *Reason);
}

// Private helper functions
void UHarmonyEngineSubsystem::PerformPeriodicAnalysis()
{
    for (const auto& PlayerPair : RegisteredPlayers)
    {
        const FString& PlayerID = PlayerPair.Key;
        AnalyzePlayerBehavior(PlayerID);
    }
}

void UHarmonyEngineSubsystem::AnalyzePlayerBehavior(const FString& PlayerID)
{
    if (EmotionalIntelligence)
    {
        EEmotionalState CurrentState = EmotionalIntelligence->AnalyzeCurrentEmotionalState(PlayerID);
        
        // Check for intervention needs
        if (EmotionalIntelligence->DetectEmotionalEscalation(PlayerID))
        {
            FHarmonyInterventionData Intervention = EmotionalIntelligence->RecommendIntervention(PlayerID);
            if (Intervention.InterventionType != EInterventionType::None)
            {
                TriggerIntervention(PlayerID, Intervention.InterventionType, TEXT("Emotional escalation detected"));
            }
        }
    }
}

void UHarmonyEngineSubsystem::SavePlayerData()
{
    UE_LOG(LogHarmonyEngine, Log, TEXT("Saving Harmony Engine player data for %d players"), PlayerBehaviorData.Num());

    // Create save data structure
    FString SaveDataJson = TEXT("{\"players\":[");
    bool bFirstEntry = true;

    for (const auto& PlayerPair : PlayerBehaviorData)
    {
        if (!bFirstEntry)
        {
            SaveDataJson += TEXT(",");
        }
        bFirstEntry = false;

        const FString& PlayerID = PlayerPair.Key;
        const FPlayerBehaviorSnapshot& BehaviorData = PlayerPair.Value;

        // Convert behavior data to JSON
        SaveDataJson += FString::Printf(TEXT("{\"id\":\"%s\",\"toxicity\":%.3f,\"positivity\":%.3f,\"frustration\":%.3f,\"kindness\":%.3f,\"timestamp\":\"%s\"}"),
            *PlayerID,
            BehaviorData.ToxicityScore,
            BehaviorData.PositivityScore,
            BehaviorData.FrustrationLevel,
            PlayerKindnessScores.Contains(PlayerID) ? PlayerKindnessScores[PlayerID] : 0.0f,
            *BehaviorData.Timestamp.ToString()
        );
    }

    SaveDataJson += TEXT("]}");

    // Save to persistent storage using Unreal's save system
    if (UGameplayStatics::DoesSaveGameExist(TEXT("HarmonyEngineData"), 0))
    {
        UGameplayStatics::DeleteGameInSlot(TEXT("HarmonyEngineData"), 0);
    }

    // Create a simple save game object for the JSON data
    // In a full implementation, this would use a proper USaveGame subclass
    FString SavePath = FPaths::ProjectSavedDir() + TEXT("HarmonyEngine/PlayerBehaviorData.json");
    FFileHelper::SaveStringToFile(SaveDataJson, *SavePath);

    UE_LOG(LogHarmonyEngine, Log, TEXT("Successfully saved Harmony Engine data to: %s"), *SavePath);
}

void UHarmonyEngineSubsystem::LoadPlayerData()
{
    UE_LOG(LogHarmonyEngine, Log, TEXT("Loading Harmony Engine player data"));

    FString SavePath = FPaths::ProjectSavedDir() + TEXT("HarmonyEngine/PlayerBehaviorData.json");
    FString LoadedJson;

    if (!FFileHelper::LoadFileToString(LoadedJson, *SavePath))
    {
        UE_LOG(LogHarmonyEngine, Log, TEXT("No existing Harmony Engine data found, starting fresh"));
        return;
    }

    // Parse JSON data
    TSharedPtr<FJsonObject> JsonObject;
    TSharedRef<TJsonReader<>> Reader = TJsonReaderFactory<>::Create(LoadedJson);

    if (!FJsonSerializer::Deserialize(Reader, JsonObject) || !JsonObject.IsValid())
    {
        UE_LOG(LogHarmonyEngine, Error, TEXT("Failed to parse Harmony Engine save data"));
        return;
    }

    // Load player data
    const TArray<TSharedPtr<FJsonValue>>* PlayersArray;
    if (JsonObject->TryGetArrayField(TEXT("players"), PlayersArray))
    {
        for (const auto& PlayerValue : *PlayersArray)
        {
            TSharedPtr<FJsonObject> PlayerObject = PlayerValue->AsObject();
            if (!PlayerObject.IsValid())
            {
                continue;
            }

            FString PlayerID;
            if (!PlayerObject->TryGetStringField(TEXT("id"), PlayerID))
            {
                continue;
            }

            // Create behavior snapshot from loaded data
            FPlayerBehaviorSnapshot BehaviorData;
            BehaviorData.PlayerID = PlayerID;
            PlayerObject->TryGetNumberField(TEXT("toxicity"), BehaviorData.ToxicityScore);
            PlayerObject->TryGetNumberField(TEXT("positivity"), BehaviorData.PositivityScore);
            PlayerObject->TryGetNumberField(TEXT("frustration"), BehaviorData.FrustrationLevel);

            FString TimestampString;
            if (PlayerObject->TryGetStringField(TEXT("timestamp"), TimestampString))
            {
                FDateTime::Parse(TimestampString, BehaviorData.Timestamp);
            }

            // Load kindness score
            double KindnessScore = 0.0;
            PlayerObject->TryGetNumberField(TEXT("kindness"), KindnessScore);
            PlayerKindnessScores.Add(PlayerID, static_cast<float>(KindnessScore));

            // Store loaded data
            PlayerBehaviorData.Add(PlayerID, BehaviorData);
        }

        UE_LOG(LogHarmonyEngine, Log, TEXT("Successfully loaded data for %d players"), PlayerBehaviorData.Num());
    }
}

// Implementation of missing helper functions

void UHarmonyEngineSubsystem::AnalyzeBehaviorForIntervention(const FString& PlayerID, const FPlayerBehaviorSnapshot& BehaviorData)
{
    // Check if intervention is needed based on behavior data
    if (BehaviorData.ToxicityScore > ToxicityThreshold)
    {
        EInterventionType InterventionType = EInterventionType::Moderate;

        if (BehaviorData.ToxicityScore > 0.9f)
        {
            InterventionType = EInterventionType::Strong;
        }
        else if (BehaviorData.FrustrationLevel > 0.8f)
        {
            InterventionType = EInterventionType::Emergency;
        }

        TriggerIntervention(PlayerID, InterventionType, TEXT("High toxicity detected"));
    }
    else if (BehaviorData.FrustrationLevel > FrustrationThreshold)
    {
        TriggerIntervention(PlayerID, EInterventionType::Gentle, TEXT("Frustration level rising"));
    }
}

bool UHarmonyEngineSubsystem::IsPlayerOnInterventionCooldown(const FString& PlayerID)
{
    if (!LastInterventionTimes.Contains(PlayerID))
    {
        return false;
    }

    FDateTime LastIntervention = LastInterventionTimes[PlayerID];
    FDateTime CurrentTime = FDateTime::Now();
    FTimespan TimeSinceLastIntervention = CurrentTime - LastIntervention;

    return TimeSinceLastIntervention < InterventionCooldown;
}

FString UHarmonyEngineSubsystem::GenerateInterventionMessage(EInterventionType InterventionType, const FString& Reason)
{
    FString Message;

    switch (InterventionType)
    {
        case EInterventionType::Gentle:
            Message = FString::Printf(TEXT("Hey there! We noticed you might be feeling a bit frustrated. Take a deep breath - you've got this! 🌟"));
            break;

        case EInterventionType::Moderate:
            Message = FString::Printf(TEXT("We understand gaming can be intense sometimes. Would you like to take a quick break or try a different approach? Your team believes in you! 💪"));
            break;

        case EInterventionType::Strong:
            Message = FString::Printf(TEXT("It looks like you're having a tough time. Remember, every pro player has bad games. Let's focus on positive teamwork and comeback together! 🤝"));
            break;

        case EInterventionType::Emergency:
            Message = FString::Printf(TEXT("We're here to support you. Gaming should be fun for everyone. Would you like to speak with a community mentor or take a wellness break? 🕊️"));
            break;

        default:
            Message = TEXT("Stay positive and have fun! 😊");
            break;
    }

    return Message;
}

float UHarmonyEngineSubsystem::CalculateInterventionPriority(EInterventionType InterventionType)
{
    switch (InterventionType)
    {
        case EInterventionType::Emergency:
            return 1.0f;
        case EInterventionType::Strong:
            return 0.8f;
        case EInterventionType::Moderate:
            return 0.6f;
        case EInterventionType::Gentle:
            return 0.4f;
        default:
            return 0.0f;
    }
}

void UHarmonyEngineSubsystem::UpdateInterventionCooldown(const FString& PlayerID)
{
    LastInterventionTimes.Add(PlayerID, FDateTime::Now());
}

float UHarmonyEngineSubsystem::CalculateExperienceMultiplier(int32 KindnessPoints)
{
    // Progressive multiplier based on kindness points
    if (KindnessPoints >= 100)
    {
        return 1.5f; // 50% bonus for exceptional kindness
    }
    else if (KindnessPoints >= 50)
    {
        return 1.3f; // 30% bonus for high kindness
    }
    else if (KindnessPoints >= 20)
    {
        return 1.2f; // 20% bonus for good kindness
    }
    else if (KindnessPoints >= 10)
    {
        return 1.1f; // 10% bonus for basic kindness
    }

    return 1.0f; // No bonus for low kindness
}

void UHarmonyEngineSubsystem::SavePlayerBehaviorData(const FString& PlayerID)
{
    if (!PlayerBehaviorData.Contains(PlayerID))
    {
        return;
    }

    const FPlayerBehaviorSnapshot& BehaviorData = PlayerBehaviorData[PlayerID];
    float KindnessScore = PlayerKindnessScores.Contains(PlayerID) ? PlayerKindnessScores[PlayerID] : 0.0f;

    // Create individual player save data
    FString PlayerDataJson = FString::Printf(
        TEXT("{\"id\":\"%s\",\"toxicity\":%.3f,\"positivity\":%.3f,\"frustration\":%.3f,\"kindness\":%.3f,\"positive_actions\":%d,\"negative_actions\":%d,\"session_duration\":%.2f,\"timestamp\":\"%s\"}"),
        *PlayerID,
        BehaviorData.ToxicityScore,
        BehaviorData.PositivityScore,
        BehaviorData.FrustrationLevel,
        KindnessScore,
        BehaviorData.PositiveActionsCount,
        BehaviorData.NegativeActionsCount,
        BehaviorData.SessionDuration,
        *BehaviorData.Timestamp.ToString()
    );

    // Save individual player file
    FString PlayerSavePath = FPaths::ProjectSavedDir() + FString::Printf(TEXT("HarmonyEngine/Players/%s.json"), *PlayerID);
    FFileHelper::SaveStringToFile(PlayerDataJson, *PlayerSavePath);

    UE_LOG(LogHarmonyEngine, VeryVerbose, TEXT("Saved behavior data for player: %s"), *PlayerID);
}

void UHarmonyEngineSubsystem::UpdateCommunityHealth()
{
    if (RegisteredPlayers.Num() == 0)
    {
        return;
    }

    // Calculate overall community health metrics
    float TotalToxicity = 0.0f;
    float TotalPositivity = 0.0f;
    float TotalKindness = 0.0f;
    int32 ActivePlayers = 0;

    for (const auto& PlayerPair : PlayerBehaviorData)
    {
        const FPlayerBehaviorSnapshot& BehaviorData = PlayerPair.Value;
        TotalToxicity += BehaviorData.ToxicityScore;
        TotalPositivity += BehaviorData.PositivityScore;

        if (PlayerKindnessScores.Contains(PlayerPair.Key))
        {
            TotalKindness += PlayerKindnessScores[PlayerPair.Key];
        }

        ActivePlayers++;
    }

    if (ActivePlayers > 0)
    {
        float AvgToxicity = TotalToxicity / ActivePlayers;
        float AvgPositivity = TotalPositivity / ActivePlayers;
        float AvgKindness = TotalKindness / ActivePlayers;

        // Calculate community health score (0-100)
        float CommunityHealthScore = FMath::Clamp(
            (AvgPositivity * 40.0f) + (AvgKindness * 0.3f) + ((1.0f - AvgToxicity) * 30.0f),
            0.0f,
            100.0f
        );

        UE_LOG(LogHarmonyEngine, Log, TEXT("Community Health Update - Score: %.2f, Avg Toxicity: %.3f, Avg Positivity: %.3f, Avg Kindness: %.2f"),
            CommunityHealthScore, AvgToxicity, AvgPositivity, AvgKindness);

        // Trigger community-wide events based on health score
        if (CommunityHealthScore > 80.0f)
        {
            // Trigger positive community event
            BroadcastCommunityEvent(TEXT("High community harmony detected! Bonus rewards activated! 🌟"));
        }
        else if (CommunityHealthScore < 30.0f)
        {
            // Trigger community healing initiative
            BroadcastCommunityEvent(TEXT("Community needs healing. Kindness initiatives activated. Let's support each other! 🤝"));
        }
    }
}

void UHarmonyEngineSubsystem::BroadcastCommunityEvent(const FString& EventMessage)
{
    // Broadcast to all registered players
    for (const auto& PlayerPair : RegisteredPlayers)
    {
        if (APlayerController* PC = PlayerPair.Value.Get())
        {
            // Send message to player (this would integrate with the UI system)
            UE_LOG(LogHarmonyEngine, Log, TEXT("Broadcasting to player %s: %s"), *PlayerPair.Key, *EventMessage);
        }
    }
}

float UHarmonyEngineSubsystem::GetCommunityHealthScore() const
{
    if (RegisteredPlayers.Num() == 0)
    {
        return 50.0f; // Neutral score when no players
    }

    // Calculate overall community health metrics
    float TotalToxicity = 0.0f;
    float TotalPositivity = 0.0f;
    float TotalKindness = 0.0f;
    int32 ActivePlayers = 0;

    for (const auto& PlayerPair : PlayerBehaviorData)
    {
        const FPlayerBehaviorSnapshot& BehaviorData = PlayerPair.Value;
        TotalToxicity += BehaviorData.ToxicityScore;
        TotalPositivity += BehaviorData.PositivityScore;

        if (PlayerKindnessScores.Contains(PlayerPair.Key))
        {
            TotalKindness += PlayerKindnessScores[PlayerPair.Key];
        }

        ActivePlayers++;
    }

    if (ActivePlayers == 0)
    {
        return 50.0f; // Neutral score when no active players
    }

    float AvgToxicity = TotalToxicity / ActivePlayers;
    float AvgPositivity = TotalPositivity / ActivePlayers;
    float AvgKindness = TotalKindness / ActivePlayers;

    // Calculate community health score (0-100)
    float CommunityHealthScore = FMath::Clamp(
        (AvgPositivity * 40.0f) + (AvgKindness * 0.3f) + ((1.0f - AvgToxicity) * 30.0f),
        0.0f,
        100.0f
    );

    return CommunityHealthScore;
}
