// AURACRON - Bridge C++ para Sistema de Transições Verticais
// Integração com Unreal Engine 5.6 APIs
// Autor: Augment Agent
// Data: 2025-08-03
// Versão: 1.0.0

#pragma once

#include "CoreMinimal.h"
#include "Components/ActorComponent.h"
#include "Engine/Engine.h"
#include "NavigationSystem.h"
#include "Components/StaticMeshComponent.h"
#include "Components/BoxComponent.h"
#include "GameFramework/Character.h"
#include "GameFramework/PlayerController.h"
#include "Kismet/GameplayStatics.h"
#include "TimerManager.h"
#include "Engine/World.h"
#include "DrawDebugHelpers.h"
#include "UObject/SoftObjectPtr.h"
#include "Engine/StaticMesh.h"
#include "Materials/MaterialInterface.h"
#include "Particles/ParticleSystem.h"
#include "Sound/SoundCue.h"
#include "AuracronVerticalTransitionsBridge.generated.h"

// Forward Declarations
class UNavigationSystemV1;
class UStaticMeshComponent;
class UBoxComponent;
class UParticleSystemComponent;
class UAudioComponent;

UENUM(BlueprintType)
enum class ERealmType : uint8
{
    PlanicieRadiante    UMETA(DisplayName = "Planície Radiante"),
    FirmamentoZephyr    UMETA(DisplayName = "Firmamento Zephyr"),
    AbismoUmbrio        UMETA(DisplayName = "Abismo Umbrio")
};

UENUM(BlueprintType)
enum class EAuracronVerticalTransitionType : uint8
{
    Portal              UMETA(DisplayName = "Portal"),
    Elevator            UMETA(DisplayName = "Elevator"),
    Stairs              UMETA(DisplayName = "Stairs"),
    JumpPad             UMETA(DisplayName = "Jump Pad"),
    TeleportCircle      UMETA(DisplayName = "Teleport Circle"),
    WindCurrent         UMETA(DisplayName = "Wind Current"),
    CaveEntrance        UMETA(DisplayName = "Cave Entrance"),
    FloatingPlatform    UMETA(DisplayName = "Floating Platform")
};

UENUM(BlueprintType)
enum class ETransitionDirection : uint8
{
    Up                  UMETA(DisplayName = "Up"),
    Down                UMETA(DisplayName = "Down"),
    Bidirectional       UMETA(DisplayName = "Bidirectional")
};

USTRUCT(BlueprintType)
struct AURACRONVERTICALTRANSITIONSBRIDGE_API FTransitionProperties
{
    GENERATED_BODY()

    FTransitionProperties()
    {
        TransitionSpeed = 5.0f;
        CooldownTime = 2.0f;
        EnergyCost = 0.0f;
        ActivationTime = 0.5f;
        MaxPlayers = 5;
        bRequiresChannel = false;
        bInterruptible = true;
        VisibilityRange = 100.0f;
        SoundRange = 50.0f;
        bVisualEffects = true;
        StrategicValue = 1.0f;
    }

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Transition Properties")
    float TransitionSpeed = 5.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Transition Properties")
    float CooldownTime = 2.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Transition Properties")
    float EnergyCost = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Transition Properties")
    float ActivationTime = 0.5f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Transition Properties")
    int32 MaxPlayers = 5;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Transition Properties")
    bool bRequiresChannel = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Transition Properties")
    bool bInterruptible = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Transition Properties")
    float VisibilityRange = 100.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Transition Properties")
    float SoundRange = 50.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Transition Properties")
    bool bVisualEffects = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Transition Properties")
    float StrategicValue = 1.0f;
};

USTRUCT(BlueprintType)
struct AURACRONVERTICALTRANSITIONSBRIDGE_API FTransitionPointData
{
    GENERATED_BODY()

    FTransitionPointData()
    {
        TransitionID = TEXT("");
        TransitionType = EAuracronVerticalTransitionType::Portal;
        Direction = ETransitionDirection::Bidirectional;
        SourceRealm = ERealmType::PlanicieRadiante;
        DestinationRealm = ERealmType::FirmamentoZephyr;
        SourceLocation = FVector::ZeroVector;
        DestinationLocation = FVector::ZeroVector;
        SourceRotation = FRotator::ZeroRotator;
        DestinationRotation = FRotator::ZeroRotator;
        bIsActive = true;
        bRequiresKey = false;
        RequiredKeyID = TEXT("");
    }

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Transition Point")
    FString TransitionID;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Transition Point")
    EAuracronVerticalTransitionType TransitionType = EAuracronVerticalTransitionType::Portal;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Transition Point")
    ETransitionDirection Direction = ETransitionDirection::Bidirectional;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Transition Point")
    ERealmType SourceRealm = ERealmType::PlanicieRadiante;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Transition Point")
    ERealmType DestinationRealm = ERealmType::FirmamentoZephyr;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Transition Point")
    FVector SourceLocation = FVector::ZeroVector;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Transition Point")
    FVector DestinationLocation = FVector::ZeroVector;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Transition Point")
    FRotator SourceRotation = FRotator::ZeroRotator;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Transition Point")
    FRotator DestinationRotation = FRotator::ZeroRotator;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Transition Point")
    FTransitionProperties Properties;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Transition Point")
    TSoftObjectPtr<UStaticMesh> TransitionMesh;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Transition Point")
    TSoftObjectPtr<UMaterialInterface> TransitionMaterial;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Transition Point")
    TSoftObjectPtr<UParticleSystem> TransitionEffect;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Transition Point")
    TSoftObjectPtr<USoundCue> TransitionSound;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Transition Point")
    bool bIsActive = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Transition Point")
    bool bRequiresKey = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Transition Point")
    FString RequiredKeyID;
};

/**
 * Estrutura wrapper para TArray<FString> em TMap
 */
USTRUCT(BlueprintType)
struct AURACRONVERTICALTRANSITIONSBRIDGE_API FStringArrayWrapper
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "String Array")
    TArray<FString> Strings;

    FStringArrayWrapper()
    {
    }

    FStringArrayWrapper(const TArray<FString>& InStrings)
        : Strings(InStrings)
    {
    }
};

USTRUCT(BlueprintType)
struct AURACRONVERTICALTRANSITIONSBRIDGE_API FTransitionNetworkData
{
    GENERATED_BODY()

    FTransitionNetworkData()
    {
        NetworkEfficiency = 1.0f;
        TotalTransitions = 0;
        bNetworkOptimized = false;
    }

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Network")
    TArray<FTransitionPointData> TransitionPoints;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Network")
    TMap<FString, FStringArrayWrapper> ConnectedTransitions;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Network")
    float NetworkEfficiency = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Network")
    int32 TotalTransitions = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Network")
    bool bNetworkOptimized = false;
};

/**
 * Classe principal do Bridge para Sistema de Transições Verticais
 * Gerencia transições entre os três realms do AURACRON
 */
UCLASS(BlueprintType, Blueprintable, Category = "AURACRON|Vertical Transitions", meta = (DisplayName = "AURACRON Vertical Transitions Bridge"))
class AURACRONVERTICALTRANSITIONSBRIDGE_API UAuracronVerticalTransitionsBridge : public UActorComponent
{
    GENERATED_BODY()

public:
    UAuracronVerticalTransitionsBridge();

protected:
    virtual void BeginPlay() override;
    virtual void EndPlay(const EEndPlayReason::Type EndPlayReason) override;

public:
    virtual void TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction) override;

    // === Core Transition Management ===

    /**
     * Criar ponto de transição
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Vertical Transitions|Management", CallInEditor)
    bool CreateTransitionPoint(const FTransitionPointData& TransitionData);

    /**
     * Remover ponto de transição
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Vertical Transitions|Management", CallInEditor)
    bool RemoveTransitionPoint(const FString& TransitionID);

    /**
     * Ativar transição
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Vertical Transitions|Management", CallInEditor)
    bool ActivateTransition(const FString& TransitionID, APawn* Player);

    /**
     * Desativar transição
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Vertical Transitions|Management", CallInEditor)
    bool DeactivateTransition(const FString& TransitionID);

    /**
     * Executar transição
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Vertical Transitions|Execution", CallInEditor)
    bool ExecuteTransition(const FString& TransitionID, APawn* Player);

    // === Network Management ===

    /**
     * Construir rede de transições
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Vertical Transitions|Network", CallInEditor)
    bool BuildTransitionNetwork();

    /**
     * Otimizar rede de transições
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Vertical Transitions|Network", CallInEditor)
    bool OptimizeTransitionNetwork();

    /**
     * Validar conectividade da rede
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Vertical Transitions|Network", CallInEditor)
    bool ValidateNetworkConnectivity();

    /**
     * Encontrar caminho entre realms
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Vertical Transitions|Network", CallInEditor)
    TArray<FString> FindPathBetweenRealms(ERealmType SourceRealm, ERealmType DestinationRealm);

    // === Player Interaction ===

    /**
     * Verificar se jogador pode usar transição
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Vertical Transitions|Player", CallInEditor)
    bool CanPlayerUseTransition(const FString& TransitionID, APawn* Player) const;

    /**
     * Obter transições próximas ao jogador
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Vertical Transitions|Player", CallInEditor)
    TArray<FString> GetNearbyTransitions(const FVector& PlayerLocation, float SearchRadius = 500.0f) const;

    /**
     * Iniciar channeling de transição
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Vertical Transitions|Player", CallInEditor)
    bool StartTransitionChanneling(const FString& TransitionID, APawn* Player);

    /**
     * Cancelar channeling de transição
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Vertical Transitions|Player", CallInEditor)
    bool CancelTransitionChanneling(APawn* Player);

    // === Visual and Audio Effects ===

    /**
     * Reproduzir efeito de transição
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Vertical Transitions|Effects", CallInEditor)
    bool PlayTransitionEffect(const FString& TransitionID, bool bActivation = true);

    /**
     * Configurar efeitos visuais
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Vertical Transitions|Effects", CallInEditor)
    bool SetupVisualEffects(const FString& TransitionID);

    /**
     * Configurar efeitos sonoros
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Vertical Transitions|Effects", CallInEditor)
    bool SetupAudioEffects(const FString& TransitionID);

public:
    // === Configuration Properties ===

    /** Dados da rede de transições */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Configuration")
    FTransitionNetworkData TransitionNetwork;

    /** Configurações padrão por tipo de transição */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Configuration")
    TMap<EAuracronVerticalTransitionType, FTransitionProperties> DefaultTransitionProperties;

    /** Usar navegação 3D */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Configuration")
    bool bUse3DNavigation = true;

    /** Raio de detecção de transições */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Configuration", meta = (ClampMin = "50.0", ClampMax = "2000.0"))
    float TransitionDetectionRadius = 200.0f;

    /** Tempo máximo de channeling */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Configuration", meta = (ClampMin = "0.1", ClampMax = "30.0"))
    float MaxChannelingTime = 10.0f;

    /** Usar efeitos visuais */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Configuration")
    bool bUseVisualEffects = true;

    /** Usar efeitos sonoros */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Configuration")
    bool bUseAudioEffects = true;

    /** Seed para geração procedural */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Configuration", meta = (ClampMin = "0", ClampMax = "999999"))
    int32 GenerationSeed = 98765;

private:
    // === Internal State ===
    
    /** Componentes de transição gerados */
    TMap<FString, TObjectPtr<UStaticMeshComponent>> TransitionComponents;
    
    /** Componentes de efeitos */
    TMap<FString, TObjectPtr<UParticleSystemComponent>> EffectComponents;
    
    /** Componentes de áudio */
    TMap<FString, TObjectPtr<UAudioComponent>> AudioComponents;
    
    /** Jogadores em channeling */
    TMap<APawn*, FString> PlayersChanneling;
    
    /** Cooldowns de transições */
    TMap<FString, float> TransitionCooldowns;
    
    /** Sistema inicializado */
    bool bSystemInitialized = false;
    
    /** Timer para atualizações */
    FTimerHandle UpdateTimer;
    
    /** Mutex para thread safety */
    mutable FCriticalSection TransitionMutex;
    
    /** Último tempo de detecção de jogadores */
    float LastPlayerDetectionTime = 0.0f;
    
    // === Internal Helper Functions ===
    
    /** Atualizar cooldowns de transições */
    void UpdateTransitionCooldowns(float DeltaTime);
    
    /** Processar channelings ativos */
    void ProcessActiveChannelings(float DeltaTime);
    
    /** Detectar jogadores próximos a transições */
    void DetectPlayersNearTransitions();

public:
    // === Python Integration ===
    
    /**
     * Inicializar bindings Python
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Vertical Transitions|Python", CallInEditor)
    bool InitializePythonBindings();
    
    /**
     * Executar script Python
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Vertical Transitions|Python", CallInEditor)
    bool ExecutePythonScript(const FString& ScriptPath);
    
    /**
     * Obter dados de transições para Python
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Vertical Transitions|Python", CallInEditor)
    FString GetTransitionDataForPython() const;

    // === Utility Functions ===
    
    /**
     * Obter estatísticas da rede
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Vertical Transitions|Utility", CallInEditor)
    FString GetNetworkStatistics() const;
    
    /**
     * Validar integridade da rede
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Vertical Transitions|Utility", CallInEditor)
    bool ValidateNetworkIntegrity() const;
    
    /**
     * Limpar rede de transições
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Vertical Transitions|Utility", CallInEditor)
    void ClearTransitionNetwork();
};

