using UnrealBuildTool;

public class AuracronHarmonyEngineBridge : ModuleRules
{
    public AuracronHarmonyEngineBridge(ReadOnlyTargetRules Target) : base(Target)
    {
        PCHUsage = PCHUsageMode.UseExplicitOrSharedPCHs;

        PublicDependencyModuleNames.AddRange(new string[]
        {
            "Core",
            "CoreUObject",
            "Engine",
            "GameplayAbilities",
            "GameplayTags",
            "ModularGameplay",
            "AIModule",
            "OnlineSubsystem",
            "OnlineSubsystemUtils",
            "Networking",
            "Sockets",
            "HTTP",
            "Json",

            "UMG",
            "Slate",
            "SlateCore",
            "InputCore",
            "EnhancedInput",
            "NavigationSystem",
            "AudioMixer",

        });

        PrivateDependencyModuleNames.AddRange(new string[]
        {
            "NetCore",
            "PacketHandler",
            "ReplicationGraph",
            "GameplayTasks",
            "Slate",
            "DeveloperSettings"
        });

        // Conditionally add editor-only dependencies
        if (Target.bBuildEditor)
        {
            PrivateDependencyModuleNames.AddRange(new string[]
            {
                "UnrealEd",
                "ToolMenus",
                "EditorStyle",
                "EditorWidgets",
                "PropertyEditor",
                "BlueprintGraph",
                "KismetCompiler",
                "GraphEditor",
                "Kismet",
                "KismetWidgets",
                "GameplayTagsEditor"
            });
        }

        // Platform-specific optimizations
        if (Target.Platform == UnrealTargetPlatform.Android || Target.Platform == UnrealTargetPlatform.IOS)
        {
            PublicDependencyModuleNames.AddRange(new string[]
            {
                "ApplicationCore",
                "Launch"
            });
        }

        // Enable RTTI for advanced AI features
        bUseRTTI = true;
        
        // Enable exceptions for error handling in AI systems
        bEnableExceptions = true;
        
        // Optimize for shipping builds
        if (Target.Configuration == UnrealTargetConfiguration.Shipping)
        {
            PublicDefinitions.Add("HARMONY_ENGINE_SHIPPING=1");
        }
        else
        {
            PublicDefinitions.Add("HARMONY_ENGINE_SHIPPING=0");
        }

        // Enable logging in development builds
        if (Target.Configuration != UnrealTargetConfiguration.Shipping)
        {
            PublicDefinitions.Add("HARMONY_ENGINE_LOGGING=1");
        }

        // Machine Learning integration defines
        PublicDefinitions.Add("HARMONY_ENGINE_ML_ENABLED=1");
        PublicDefinitions.Add("HARMONY_ENGINE_VERSION_MAJOR=1");
        PublicDefinitions.Add("HARMONY_ENGINE_VERSION_MINOR=0");
    }
}
