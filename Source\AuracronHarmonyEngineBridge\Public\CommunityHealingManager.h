#pragma once

#include "CoreMinimal.h"
#include "UObject/NoExportTypes.h"
#include "GameplayTagContainer.h"
#include "Engine/DataTable.h"
#include "AuracronHarmonyEngineBridge.h"
#include "CommunityHealingManager.generated.h"

class APlayerController;
class UAbilitySystemComponent;

UENUM(BlueprintType)
enum class EHealingSessionType : uint8
{
    PeerSupport         UMETA(DisplayName = "Peer Support"),
    Mentorship          UMETA(DisplayName = "Mentorship"),
    GroupTherapy        UMETA(DisplayName = "Group Therapy"),
    CrisisIntervention  UMETA(DisplayName = "Crisis Intervention"),
    CelebrationCircle   UMETA(DisplayName = "Celebration Circle")
};

UENUM(BlueprintType)
enum class EHealingStatus : uint8
{
    Pending             UMETA(DisplayName = "Pending"),
    Active              UMETA(DisplayName = "Active"),
    Completed           UMETA(DisplayName = "Completed"),
    Cancelled           UMETA(DisplayName = "Cancelled"),
    Failed              UMETA(DisplayName = "Failed")
};

USTRUCT(BlueprintType)
struct AURACRONHARMONYENGINEBRIDGE_API FHealingSession
{
    GENERATED_BODY()

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    FString SessionID;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    EHealingSessionType SessionType;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    EHealingStatus Status;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    FString VictimPlayerID;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    TArray<FString> HealerPlayerIDs;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    FDateTime StartTime;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    FDateTime EndTime;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    float HealingProgress;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    FString HealingGoal;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    FGameplayTagContainer SessionTags;

    FHealingSession()
    {
        SessionID = FGuid::NewGuid().ToString();
        SessionType = EHealingSessionType::PeerSupport;
        Status = EHealingStatus::Pending;
        VictimPlayerID = TEXT("");
        StartTime = FDateTime::Now();
        EndTime = FDateTime::MinValue();
        HealingProgress = 0.0f;
        HealingGoal = TEXT("");
    }
};

USTRUCT(BlueprintType)
struct AURACRONHARMONYENGINEBRIDGE_API FHealerProfile
{
    GENERATED_BODY()

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    FString PlayerID;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    float HealingSkillLevel;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    int32 SuccessfulHealingSessions;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    float AverageSessionRating;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    TArray<EHealingSessionType> Specializations;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    bool bIsAvailable;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    FDateTime LastActiveTime;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    FGameplayTagContainer HealerTags;

    FHealerProfile()
    {
        PlayerID = TEXT("");
        HealingSkillLevel = 0.0f;
        SuccessfulHealingSessions = 0;
        AverageSessionRating = 0.0f;
        bIsAvailable = true;
        LastActiveTime = FDateTime::Now();
    }
};

/**
 * Wrapper struct for TArray to use in TMap with UPROPERTY
 */
USTRUCT(BlueprintType)
struct AURACRONHARMONYENGINEBRIDGE_API FPlayerHealingHistory
{
    GENERATED_BODY()

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    TArray<FString> SessionIDs;

    FPlayerHealingHistory()
    {
        SessionIDs.Empty();
    }
};

/**
 * Community Healing Manager
 * Manages peer support, mentorship programs, and community healing initiatives
 */
UCLASS(BlueprintType, Blueprintable)
class AURACRONHARMONYENGINEBRIDGE_API UCommunityHealingManager : public UObject
{
    GENERATED_BODY()

public:
    UCommunityHealingManager();

    // Core Healing Functions
    UFUNCTION(BlueprintCallable, Category = "Community Healing")
    FString InitiateHealingSession(const FString& VictimPlayerID, EHealingSessionType SessionType);

    UFUNCTION(BlueprintCallable, Category = "Community Healing")
    bool AddHealerToSession(const FString& SessionID, const FString& HealerPlayerID);

    UFUNCTION(BlueprintCallable, Category = "Community Healing")
    bool CompleteHealingSession(const FString& SessionID, float SuccessRating);

    UFUNCTION(BlueprintCallable, Category = "Community Healing")
    void CancelHealingSession(const FString& SessionID, const FString& Reason);

    // Healer Management
    UFUNCTION(BlueprintCallable, Category = "Community Healing")
    void RegisterHealer(const FString& PlayerID, const TArray<EHealingSessionType>& Specializations);

    UFUNCTION(BlueprintCallable, Category = "Community Healing")
    void UnregisterHealer(const FString& PlayerID);

    UFUNCTION(BlueprintCallable, Category = "Community Healing")
    TArray<FString> FindAvailableHealers(EHealingSessionType SessionType);

    UFUNCTION(BlueprintCallable, Category = "Community Healing")
    FString FindBestMatchedHealer(const FString& VictimPlayerID, EHealingSessionType SessionType);

    // Session Management
    UFUNCTION(BlueprintCallable, Category = "Community Healing")
    TArray<FHealingSession> GetActiveHealingSessions();

    UFUNCTION(BlueprintCallable, Category = "Community Healing")
    FHealingSession GetHealingSession(const FString& SessionID);

    UFUNCTION(BlueprintCallable, Category = "Community Healing")
    bool IsPlayerInHealingSession(const FString& PlayerID);

    UFUNCTION(BlueprintCallable, Category = "Community Healing")
    void UpdateHealingProgress(const FString& SessionID, float Progress);

    // Analytics and Reporting
    UFUNCTION(BlueprintCallable, Category = "Community Healing")
    float GetCommunityHealingEffectiveness();

    UFUNCTION(BlueprintCallable, Category = "Community Healing")
    int32 GetTotalHealingSessionsCompleted();

    UFUNCTION(BlueprintCallable, Category = "Community Healing")
    TArray<FString> GetTopHealers(int32 Count = 10);

    UFUNCTION(BlueprintCallable, Category = "Community Healing")
    float GetHealerRating(const FString& HealerPlayerID);

protected:
    // Configuration
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Healing Config")
    int32 MaxConcurrentSessions;

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Healing Config")
    float MaxSessionDuration;

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Healing Config")
    float MinHealerSkillLevel;

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Healing Config")
    int32 MaxHealersPerSession;

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Healing Config")
    bool bEnableAutomaticMatching;

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Healing Config")
    bool bEnableSessionRecording;

    // Data Storage
    UPROPERTY()
    TMap<FString, FHealingSession> ActiveSessions;

    UPROPERTY()
    TMap<FString, FHealerProfile> RegisteredHealers;

    UPROPERTY()
    TArray<FHealingSession> CompletedSessions;

    UPROPERTY()
    TMap<FString, FPlayerHealingHistory> PlayerHealingHistory;

    // Session Timers
    UPROPERTY()
    TMap<FString, FTimerHandle> SessionTimers;

private:
    // Session management helpers
    bool ValidateHealingRequest(const FString& VictimPlayerID, EHealingSessionType SessionType);
    bool ValidateHealerEligibility(const FString& HealerPlayerID, EHealingSessionType SessionType);
    void StartSessionTimer(const FString& SessionID);
    void OnSessionTimeout(const FString& SessionID);
    
    // Matching algorithms
    float CalculateHealerCompatibility(const FString& HealerPlayerID, const FString& VictimPlayerID);
    TArray<FString> RankHealersByCompatibility(const TArray<FString>& AvailableHealers, const FString& VictimPlayerID);
    bool IsHealerSpecializedFor(const FString& HealerPlayerID, EHealingSessionType SessionType);
    
    // Progress tracking
    void MonitorSessionProgress(const FString& SessionID);
    void UpdateHealerStatistics(const FString& HealerPlayerID, float SessionRating);
    void RecordSessionOutcome(const FHealingSession& Session, bool bSuccessful);
    
    // Quality assurance
    bool ValidateSessionQuality(const FString& SessionID);
    void ProvideHealerFeedback(const FString& HealerPlayerID, const FString& Feedback);
    void EscalateIfNeeded(const FString& SessionID);
    
    // Data persistence
    void SaveHealingData();
    void LoadHealingData();
    void ArchiveCompletedSessions();
    
    // Utility functions
    FString GenerateSessionID();
    FString GenerateHealingGoal(EHealingSessionType SessionType, const FString& VictimPlayerID);
    float CalculateInitialHealerSkill(const FString& PlayerID);
    EHealingSessionType DetermineOptimalSessionType(const FString& VictimPlayerID);
    float CalculateSessionSuccessProbability(const FHealingSession& Session);
};
