// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON - Sistema de Geometria Virtualizada Nanite Bridge Implementation

#include "AuracronNaniteBridge.h"
#include "Engine/Engine.h"
#include "Engine/World.h"
#include "Components/StaticMeshComponent.h"
#include "Components/InstancedStaticMeshComponent.h"
#include "Engine/StaticMesh.h"
#include "Materials/MaterialInstanceDynamic.h"
#include "ProceduralMeshComponent.h"
#include "TimerManager.h"
#include "Net/UnrealNetwork.h"
#include "Kismet/GameplayStatics.h"

UAuracronNaniteBridge::UAuracronNaniteBridge()
{
    PrimaryComponentTick.bCanEverTick = true;
    PrimaryComponentTick.TickInterval = 1.0f; // 1 FPS para geometria (não precisa ser frequente)
    
    // Configurar replicação
    SetIsReplicatedByDefault(true);
    
    // Configurações padrão de Nanite
    NaniteConfiguration.bUseNanite = true;
    NaniteConfiguration.NaniteQuality = EAuracronNaniteQuality::High;
    NaniteConfiguration.MaxRenderDistance = 20000.0f;
    NaniteConfiguration.bUseAggressiveCulling = true;
    NaniteConfiguration.LODBias = 0.0f;
    NaniteConfiguration.bUseMeshStreaming = true;
    NaniteConfiguration.StreamingPoolSizeMB = 512;
    NaniteConfiguration.bUseMeshCompression = true;
    NaniteConfiguration.CompressionLevel = 7;
    NaniteConfiguration.bUseFallbackForMobile = true;
    NaniteConfiguration.bUseAutoInstancing = true;
    NaniteConfiguration.InstancingThreshold = 10;
    NaniteConfiguration.bUseOcclusionCulling = true;
    NaniteConfiguration.bUseFrustumCulling = true;
    NaniteConfiguration.bUseDistanceCulling = true;
    NaniteConfiguration.CullingDistance = 25000.0f;
    
    // Configurações padrão de geometria de realm
    RealmGeometryConfiguration.RealmGeometryDensity = {1.0f, 1.2f, 0.8f};
    RealmGeometryConfiguration.bUseProceduralGeometry = true;
    RealmGeometryConfiguration.ProceduralSeed = 12345;
    RealmGeometryConfiguration.ProceduralComplexity = 1.0f;
    RealmGeometryConfiguration.bUseMeshVariations = true;
    RealmGeometryConfiguration.MeshVariations = 3;
}

void UAuracronNaniteBridge::BeginPlay()
{
    Super::BeginPlay();

    UE_LOG(LogTemp, Warning, TEXT("AURACRON: Inicializando Sistema de Geometria Nanite"));

    // Inicializar sistema
    bSystemInitialized = InitializeNaniteSystem();
    
    if (bSystemInitialized)
    {
        // Configurar timers
        GetWorld()->GetTimerManager().SetTimer(
            OptimizationTimer,
            [this]()
            {
                if (APlayerController* PC = GetWorld()->GetFirstPlayerController())
                {
                    if (APawn* Pawn = PC->GetPawn())
                    {
                        OptimizeGeometryByDistance(Pawn->GetActorLocation());
                    }
                }
            },
            2.0f, // A cada 2 segundos
            true
        );

        GetWorld()->GetTimerManager().SetTimer(
            CleanupTimer,
            [this]()
            {
                CleanupUnusedGeometry();
            },
            30.0f, // A cada 30 segundos
            true
        );
        
        UE_LOG(LogTemp, Log, TEXT("AURACRON: Sistema de Geometria Nanite inicializado com sucesso"));
    }
    else
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Falha ao inicializar Sistema de Geometria Nanite"));
    }
}

void UAuracronNaniteBridge::EndPlay(const EEndPlayReason::Type EndPlayReason)
{
    // Limpar componentes ativos
    for (UStaticMeshComponent* Component : ActiveMeshComponents)
    {
        if (IsValid(Component))
        {
            Component->DestroyComponent();
        }
    }
    ActiveMeshComponents.Empty();
    
    // Limpar componentes instanciados
    for (UInstancedStaticMeshComponent* Component : ActiveInstancedComponents)
    {
        if (IsValid(Component))
        {
            Component->DestroyComponent();
        }
    }
    ActiveInstancedComponents.Empty();
    
    // Limpar instâncias
    ActiveGeometryInstances.Empty();

    // Limpar timers
    if (GetWorld())
    {
        GetWorld()->GetTimerManager().ClearTimer(OptimizationTimer);
        GetWorld()->GetTimerManager().ClearTimer(CleanupTimer);
    }

    Super::EndPlay(EndPlayReason);
}

void UAuracronNaniteBridge::GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const
{
    Super::GetLifetimeReplicatedProps(OutLifetimeProps);

    DOREPLIFETIME(UAuracronNaniteBridge, NaniteConfiguration);
}

void UAuracronNaniteBridge::TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction)
{
    Super::TickComponent(DeltaTime, TickType, ThisTickFunction);

    if (!bSystemInitialized)
        return;

    // Processar geometria ativa
    ProcessActiveGeometry(DeltaTime);
}

// === Core Nanite Management ===

UStaticMeshComponent* UAuracronNaniteBridge::SpawnNaniteGeometry(const FAuracronGeometryInstance& GeometryConfig)
{
    if (!bSystemInitialized || !GeometryConfig.Mesh.IsValid())
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Sistema não inicializado ou mesh inválido"));
        return nullptr;
    }

    if (!ValidateGeometryConfiguration(GeometryConfig))
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Configuração de geometria inválida"));
        return nullptr;
    }

    // Carregar mesh
    UStaticMesh* StaticMesh = GeometryConfig.Mesh.LoadSynchronous();
    if (!StaticMesh)
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Falha ao carregar mesh"));
        return nullptr;
    }

    // Criar componente
    UStaticMeshComponent* MeshComponent = NewObject<UStaticMeshComponent>(GetOwner());
    if (!MeshComponent)
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Falha ao criar StaticMeshComponent"));
        return nullptr;
    }

    // Configurar mesh
    MeshComponent->SetStaticMesh(StaticMesh);
    MeshComponent->SetWorldTransform(GeometryConfig.InstanceTransform);

    // Aplicar configurações de Nanite
    ApplyNaniteSettings(MeshComponent, NaniteConfiguration);

    // Aplicar material override se especificado
    if (GeometryConfig.MaterialOverride.IsValid())
    {
        UMaterialInterface* Material = GeometryConfig.MaterialOverride.LoadSynchronous();
        if (Material)
        {
            MeshComponent->SetMaterial(0, Material);
        }
    }

    // Configurar culling
    if (GeometryConfig.bUseDistanceCulling)
    {
        MeshComponent->SetCullDistance(GeometryConfig.CullingDistance);
    }

    // Anexar ao owner
    MeshComponent->AttachToComponent(
        GetOwner()->GetRootComponent(),
        FAttachmentTransformRules::KeepWorldTransform
    );

    // Registrar componente
    GetOwner()->AddInstanceComponent(MeshComponent);
    MeshComponent->RegisterComponent();

    // Adicionar às listas de controle
    ActiveMeshComponents.Add(MeshComponent);
    ActiveGeometryInstances.Add(GeometryConfig);

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Geometria Nanite spawnada: %s"), *GeometryConfig.InstanceID);

    // Broadcast evento
    OnGeometrySpawned.Broadcast(MeshComponent, GeometryConfig);

    return MeshComponent;
}

bool UAuracronNaniteBridge::ConvertMeshToNanite(UStaticMesh* SourceMesh)
{
    if (!bSystemInitialized || !SourceMesh)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Sistema não inicializado ou mesh inválido"));
        return false;
    }

    // Verificar se já é Nanite
    if (SourceMesh->NaniteSettings.bEnabled)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Mesh já está habilitado para Nanite"));
        return true;
    }

    // Habilitar Nanite
    SourceMesh->NaniteSettings.bEnabled = true;
    SourceMesh->NaniteSettings.PositionPrecision = ENanitePositionPrecision::Auto;
    SourceMesh->NaniteSettings.NormalPrecision = ENaniteNormalPrecision::Auto;
    SourceMesh->NaniteSettings.TangentPrecision = ENaniteTangentPrecision::Auto;

    // Marcar para rebuild
    SourceMesh->PostEditChange();

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Mesh convertido para Nanite: %s"), *SourceMesh->GetName());

    return true;
}

bool UAuracronNaniteBridge::OptimizeMeshForNanite(UStaticMesh* TargetMesh, int32 TargetTriangles)
{
    if (!bSystemInitialized || !TargetMesh)
    {
        return false;
    }

    // Configurar settings de otimização para Nanite
    TargetMesh->NaniteSettings.bEnabled = true;
    TargetMesh->NaniteSettings.PositionPrecision = ENanitePositionPrecision::Auto;
    
    // Configurar LOD settings
    if (TargetMesh->GetNumSourceModels() > 0)
    {
        FStaticMeshSourceModel& SourceModel = TargetMesh->GetSourceModel(0);
        SourceModel.ReductionSettings.PercentTriangles = FMath::Clamp(float(TargetTriangles) / SourceModel.GetNumTriangles(), 0.1f, 1.0f);
    }

    TargetMesh->PostEditChange();

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Mesh otimizado para Nanite: %s (Target: %d triângulos)"), *TargetMesh->GetName(), TargetTriangles);

    return true;
}

bool UAuracronNaniteBridge::GenerateNaniteLODs(UStaticMesh* TargetMesh, int32 NumLODs)
{
    if (!bSystemInitialized || !TargetMesh || NumLODs <= 0)
    {
        return false;
    }

    // Configurar LODs para Nanite
    TargetMesh->SetNumSourceModels(NumLODs);

    for (int32 LODIndex = 0; LODIndex < NumLODs; LODIndex++)
    {
        FStaticMeshSourceModel& SourceModel = TargetMesh->GetSourceModel(LODIndex);

        // Configurar redução progressiva
        float ReductionPercentage = FMath::Pow(0.5f, LODIndex); // 50% de redução por LOD
        SourceModel.ReductionSettings.PercentTriangles = ReductionPercentage;
        SourceModel.ReductionSettings.PercentVertices = ReductionPercentage;
    }

    TargetMesh->PostEditChange();

    UE_LOG(LogTemp, Log, TEXT("AURACRON: LODs Nanite gerados para mesh: %s (%d LODs)"), *TargetMesh->GetName(), NumLODs);

    return true;
}

// === Instanced Geometry ===

UInstancedStaticMeshComponent* UAuracronNaniteBridge::CreateGeometryInstances(UStaticMesh* Mesh, const TArray<FTransform>& Transforms)
{
    if (!bSystemInitialized || !Mesh || Transforms.Num() == 0)
    {
        return nullptr;
    }

    // Criar componente instanciado
    UInstancedStaticMeshComponent* InstancedComponent = NewObject<UInstancedStaticMeshComponent>(GetOwner());
    if (!InstancedComponent)
    {
        return nullptr;
    }

    // Configurar mesh
    InstancedComponent->SetStaticMesh(Mesh);

    // Aplicar configurações de Nanite
    ApplyNaniteSettings(InstancedComponent, NaniteConfiguration);

    // Adicionar instâncias
    for (const FTransform& Transform : Transforms)
    {
        InstancedComponent->AddInstance(Transform);
    }

    // Anexar ao owner
    InstancedComponent->AttachToComponent(
        GetOwner()->GetRootComponent(),
        FAttachmentTransformRules::KeepWorldTransform
    );

    // Registrar componente
    GetOwner()->AddInstanceComponent(InstancedComponent);
    InstancedComponent->RegisterComponent();

    // Adicionar à lista de controle
    ActiveInstancedComponents.Add(InstancedComponent);

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Instâncias de geometria criadas: %d instâncias"), Transforms.Num());

    return InstancedComponent;
}

// === Internal Methods ===

bool UAuracronNaniteBridge::InitializeNaniteSystem()
{
    if (bSystemInitialized)
    {
        return true;
    }

    // Configurar streaming de mesh
    if (!SetupMeshStreaming())
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Falha ao configurar streaming de mesh"));
        return false;
    }

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Sistema Nanite inicializado"));

    return true;
}

bool UAuracronNaniteBridge::SetupMeshStreaming()
{
    if (!NaniteConfiguration.bUseMeshStreaming)
    {
        return true;
    }

    // Configurar pool de streaming
    if (UWorld* World = GetWorld())
    {
        // Configurações de streaming seriam aplicadas aqui
        UE_LOG(LogTemp, Log, TEXT("AURACRON: Streaming de mesh configurado - Pool: %d MB"), NaniteConfiguration.StreamingPoolSizeMB);
    }

    return true;
}

void UAuracronNaniteBridge::ProcessActiveGeometry(float DeltaTime)
{
    FScopeLock Lock(&NaniteMutex);

    // Atualizar estatísticas de Nanite
    NaniteStatistics.Empty();
    NaniteStatistics.Add(TEXT("ActiveMeshComponents"), float(ActiveMeshComponents.Num()));
    NaniteStatistics.Add(TEXT("ActiveInstancedComponents"), float(ActiveInstancedComponents.Num()));
    NaniteStatistics.Add(TEXT("TotalGeometryInstances"), float(ActiveGeometryInstances.Num()));
}

bool UAuracronNaniteBridge::ValidateGeometryConfiguration(const FAuracronGeometryInstance& Config) const
{
    if (Config.InstanceID.IsEmpty() || !Config.Mesh.IsValid())
    {
        return false;
    }

    if (Config.CullingDistance <= 0.0f || Config.RenderPriority < 0)
    {
        return false;
    }

    return true;
}

bool UAuracronNaniteBridge::ApplyNaniteSettings(UStaticMeshComponent* MeshComponent, const FAuracronNaniteConfiguration& Config)
{
    if (!MeshComponent)
    {
        return false;
    }

    // Configurar culling
    if (Config.bUseDistanceCulling)
    {
        MeshComponent->SetCullDistance(Config.CullingDistance);
    }

    // Configurar LOD bias
    MeshComponent->SetForcedLodModel(FMath::RoundToInt(Config.LODBias));

    return true;
}
