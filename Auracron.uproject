{"FileVersion": 3, "EngineAssociation": "5.6", "Category": "Games", "Description": "AURACRON - Revolutionary MOBA 5v5 with dynamic multidimensional maps and procedural content generation", "Modules": [{"Name": "<PERSON><PERSON><PERSON>", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>"}, {"Name": "AuracronPCGBridge", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>"}, {"Name": "AuracronMetaHumanBridge", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>"}, {"Name": "AuracronWorldPartitionBridge", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>"}, {"Name": "AuracronFoliageBridge", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>"}, {"Name": "AuracronLumenBridge", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>"}, {"Name": "AuracronAbismoUmbrioBridge", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>"}, {"Name": "AuracronAdaptiveCreaturesBridge", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>"}, {"Name": "AuracronVerticalTransitionsBridge", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>"}, {"Name": "AuracronQABridge", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>"}, {"Name": "AuracronMetaHumanFramework", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>"}, {"Name": "AuracronAnalyticsBridge", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>"}, {"Name": "AuracronAudioBridge", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>"}, {"Name": "AuracronVFXBridge", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>"}, {"Name": "AuracronPhysicsBridge", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>"}, {"Name": "AuracronNaniteBridge", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>"}, {"Name": "AuracronVoiceBridge", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>"}, {"Name": "AuracronAntiCheatBridge", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>"}, {"Name": "AuracronAutomatedQABridge", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>"}, {"Name": "AuracronTutorialBridge", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>"}, {"Name": "AuracronLoreBridge", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>"}, {"Name": "AuracronMonetizationBridge", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>"}, {"Name": "AuracronEOSBridge", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>"}, {"Name": "AuracronDynamicRealmBridge", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>"}, {"Name": "Auracron<PERSON><PERSON>los<PERSON>ridge", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>"}, {"Name": "AuracronHarmonyEngineBridge", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>"}, {"Name": "AuracronMasterOrchestrator", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>"}], "Plugins": [{"Name": "ModelingToolsEditorMode", "Enabled": true, "TargetAllowList": ["Editor"]}, {"Name": "PCG", "Enabled": true}, {"Name": "PCGGeometryScriptInterop", "Enabled": true}, {"Name": "<PERSON><PERSON><PERSON><PERSON>", "Enabled": true}, {"Name": "PythonScriptPlugin", "Enabled": true}, {"Name": "EditorScriptingUtilities", "Enabled": true}, {"Name": "SequencerScripting", "Enabled": true}, {"Name": "OnlineSubsystem", "Enabled": true}, {"Name": "OnlineSubsystemUtils", "Enabled": true}, {"Name": "GameplayAbilities", "Enabled": true}, {"Name": "EnhancedInput", "Enabled": true}, {"Name": "CommonUI", "Enabled": true}, {"Name": "Niagara", "Enabled": true}, {"Name": "WaveTable", "Enabled": true}, {"Name": "Synthesis", "Enabled": true}, {"Name": "OnlineSubsystemEOS", "Enabled": true}, {"Name": "EOSShared", "Enabled": true}, {"Name": "SocketSubsystemEOS", "Enabled": true}, {"Name": "GeometryScripting", "Enabled": true}, {"Name": "GeometryProcessing", "Enabled": true}, {"Name": "MeshModelingToolset", "Enabled": true}, {"Name": "MassGameplay", "Enabled": true}, {"Name": "MassAI", "Enabled": true}, {"Name": "StateTree", "Enabled": true}, {"Name": "GameplayStateTree", "Enabled": true}, {"Name": "StructUtils", "Enabled": true}, {"Name": "SmartObjects", "Enabled": true}, {"Name": "ZoneGraph", "Enabled": true}, {"Name": "ZoneGraphAnnotations", "Enabled": true}, {"Name": "AnalyticsMulticast", "Enabled": true}, {"Name": "ModularGameplay", "Enabled": true}, {"Name": "ReplicationGraph", "Enabled": true}, {"Name": "NetcodeUnitTest", "Enabled": true}, {"Name": "RawInput", "Enabled": true}, {"Name": "GPULightmass", "Enabled": true}, {"Name": "HairStrands", "Enabled": true}, {"Name": "LiveLink", "Enabled": true}, {"Name": "AudioSynesthesia", "Enabled": true}, {"Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Enabled": true}, {"Name": "Takes", "Enabled": true}, {"Name": "RemoteControl", "Enabled": true}, {"Name": "VirtualCamera", "Enabled": true}, {"Name": "ProxyLODPlugin", "Enabled": true}, {"Name": "ChaosVehiclesPlugin", "Enabled": true}, {"Name": "Water", "Enabled": true}, {"Name": "SignificanceManager", "Enabled": true}, {"Name": "StylusInput", "Enabled": true}], "TargetPlatforms": ["Windows", "Android", "IOS"]}