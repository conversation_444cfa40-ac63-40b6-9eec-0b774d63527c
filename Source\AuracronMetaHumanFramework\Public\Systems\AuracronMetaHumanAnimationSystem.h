﻿// AURACRON MetaHuman Animation System - Advanced Animation Processing for UE 5.6
// Handles MetaHuman animation generation, optimization, and real-time processing
// Author: Augment Agent
// Date: 2025-08-05
// Version: 2.0.0

#pragma once

#include "CoreMinimal.h"
#include "UObject/Object.h"
#include "Engine/Engine.h"
#include "Animation/AnimSequence.h"
#include "Animation/AnimBlueprint.h"
#include "Animation/AnimInstance.h"
#include "Animation/BlendSpace.h"
#include "Animation/AnimMontage.h"
#include "Components/SkeletalMeshComponent.h"
#include "Engine/SkeletalMesh.h"
#include "Animation/Skeleton.h"
#include "Animation/AnimationAsset.h"
#include "Animation/AnimComposite.h"
#include "Animation/BlendSpace1D.h"
// Forward declaration for BlendSpace2D compatibility
class UBlendSpace2D;
#include "Animation/AimOffsetBlendSpace.h"
#include "Animation/AimOffsetBlendSpace1D.h"
#include "Async/AsyncWork.h"

#include "AuracronMetaHumanAnimationSystem.generated.h"

DECLARE_LOG_CATEGORY_EXTERN(LogAuracronMetaHumanAnimation, Log, All);

// Forward declarations
class UAuracronMetaHumanFramework;

/**
 * Animation Processing Types
 */
UENUM(BlueprintType)
enum class EAuracronAnimationType : uint8
{
    Facial              UMETA(DisplayName = "Facial Animation"),
    Body                UMETA(DisplayName = "Body Animation"),
    Hand                UMETA(DisplayName = "Hand Animation"),
    Eye                 UMETA(DisplayName = "Eye Animation"),
    Lip                 UMETA(DisplayName = "Lip Sync Animation"),
    Emotion             UMETA(DisplayName = "Emotion Animation"),
    Gesture             UMETA(DisplayName = "Gesture Animation"),
    Locomotion          UMETA(DisplayName = "Locomotion Animation"),
    Idle                UMETA(DisplayName = "Idle Animation"),
    Transition          UMETA(DisplayName = "Transition Animation")
};

/**
 * Animation Quality Levels
 */
UENUM(BlueprintType)
enum class EAuracronAnimationQuality : uint8
{
    Draft               UMETA(DisplayName = "Draft Quality"),
    Preview             UMETA(DisplayName = "Preview Quality"),
    Production          UMETA(DisplayName = "Production Quality"),
    Cinematic           UMETA(DisplayName = "Cinematic Quality"),
    Custom              UMETA(DisplayName = "Custom Quality")
};

/**
 * Animation Generation Parameters
 */
USTRUCT(BlueprintType)
struct AURACRONMETAHUMANFRAMEWORK_API FAuracronAnimationParams
{
    GENERATED_BODY()

    /** Animation type to generate */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation")
    EAuracronAnimationType AnimationType = EAuracronAnimationType::Facial;

    /** Quality level */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation")
    EAuracronAnimationQuality Quality = EAuracronAnimationQuality::Production;

    /** Duration in seconds */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation", meta = (ClampMin = "0.1", ClampMax = "300.0"))
    float Duration = 5.0f;

    /** Frame rate */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation", meta = (ClampMin = "24", ClampMax = "120"))
    int32 FrameRate = 30;

    /** Enable root motion */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation")
    bool bEnableRootMotion = false;

    /** Loop animation */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation")
    bool bLooping = false;

    /** Additive animation */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation")
    bool bAdditive = false;

    /** Compression settings */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation")
    FString CompressionScheme = TEXT("DefaultCompressionScheme");

    /** Additional parameters as JSON */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation")
    FString AdditionalParams;

    FAuracronAnimationParams()
    {
        // Default constructor
    }
};

/**
 * Animation Processing Result
 */
USTRUCT(BlueprintType)
struct AURACRONMETAHUMANFRAMEWORK_API FAuracronAnimationResult
{
    GENERATED_BODY()

    /** Whether the operation was successful */
    UPROPERTY(BlueprintReadOnly, Category = "Result")
    bool bSuccess = false;

    /** Result message */
    UPROPERTY(BlueprintReadOnly, Category = "Result")
    FString Message;

    /** Generated animation asset */
    UPROPERTY(BlueprintReadOnly, Category = "Result")
    TObjectPtr<UAnimationAsset> GeneratedAnimation;

    /** Processing time in milliseconds */
    UPROPERTY(BlueprintReadOnly, Category = "Result")
    float ProcessingTimeMS = 0.0f;

    /** Output file path */
    UPROPERTY(BlueprintReadOnly, Category = "Result")
    FString OutputPath;

    /** Animation statistics */
    UPROPERTY(BlueprintReadOnly, Category = "Result")
    FString AnimationStats;

    FAuracronAnimationResult()
    {
        // Default constructor
    }

    FAuracronAnimationResult(bool bInSuccess, const FString& InMessage)
        : bSuccess(bInSuccess)
        , Message(InMessage)
    {
    }
};

/**
 * Facial Animation Configuration
 */
USTRUCT(BlueprintType)
struct AURACRONMETAHUMANFRAMEWORK_API FAuracronFacialAnimConfig
{
    GENERATED_BODY()

    /** Audio file for lip sync */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Facial")
    FString AudioFilePath;

    /** Text for lip sync */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Facial")
    FString LipSyncText;

    /** Emotion intensity */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Facial", meta = (ClampMin = "0.0", ClampMax = "1.0"))
    float EmotionIntensity = 0.5f;

    /** Eye blink frequency */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Facial", meta = (ClampMin = "0.0", ClampMax = "10.0"))
    float BlinkFrequency = 2.0f;

    /** Eye look target */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Facial")
    FVector EyeLookTarget = FVector::ZeroVector;

    /** Enable micro expressions */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Facial")
    bool bEnableMicroExpressions = true;

    FAuracronFacialAnimConfig()
    {
        // Default constructor
    }
};

DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FAuracronAnimationComplete, const FAuracronAnimationResult&, Result, const FString&, OperationID);

/**
 * AURACRON MetaHuman Animation System
 * Advanced animation processing and generation system for UE 5.6
 */
UCLASS(BlueprintType, Blueprintable, Category = "AURACRON|MetaHuman|Animation")
class AURACRONMETAHUMANFRAMEWORK_API UAuracronMetaHumanAnimationSystem : public UObject
{
    GENERATED_BODY()

public:
    UAuracronMetaHumanAnimationSystem();
    virtual ~UAuracronMetaHumanAnimationSystem();

    // === Core Animation Operations ===

    /**
     * Generate animation from parameters
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|Animation|Core", CallInEditor)
    FAuracronAnimationResult GenerateAnimation(const FAuracronAnimationParams& Params, USkeletalMesh* TargetMesh);

    /**
     * Generate facial animation
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|Animation|Core", CallInEditor)
    FAuracronAnimationResult GenerateFacialAnimation(const FAuracronFacialAnimConfig& Config, USkeletalMesh* TargetMesh);

    /**
     * Optimize animation
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|Animation|Core", CallInEditor)
    FAuracronAnimationResult OptimizeAnimation(UAnimationAsset* Animation);

    /**
     * Retarget animation to different skeleton
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|Animation|Core", CallInEditor)
    FAuracronAnimationResult RetargetAnimation(UAnimationAsset* SourceAnimation, USkeleton* TargetSkeleton);

    // === Async Animation Operations ===

    /**
     * Generate animation asynchronously
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|Animation|Async", CallInEditor)
    FString GenerateAnimationAsync(const FAuracronAnimationParams& Params, USkeletalMesh* TargetMesh);

    /**
     * Generate facial animation asynchronously
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|Animation|Async", CallInEditor)
    FString GenerateFacialAnimationAsync(const FAuracronFacialAnimConfig& Config, USkeletalMesh* TargetMesh);

    /**
     * Check if async operation is complete
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|Animation|Async", CallInEditor)
    bool IsAsyncOperationComplete(const FString& OperationID) const;

    /**
     * Get async operation result
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|Animation|Async", CallInEditor)
    FAuracronAnimationResult GetAsyncOperationResult(const FString& OperationID);

    // === Animation Blending ===

    /**
     * Create blend space from animations
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|Animation|Blending", CallInEditor)
    FAuracronAnimationResult CreateBlendSpace(const TArray<UAnimSequence*>& Animations, const TArray<FVector2D>& BlendParameters);

    /**
     * Create animation montage
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|Animation|Blending", CallInEditor)
    FAuracronAnimationResult CreateAnimationMontage(const TArray<UAnimSequence*>& Animations, const TArray<float>& SectionLengths);

    /**
     * Blend animations
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|Animation|Blending", CallInEditor)
    FAuracronAnimationResult BlendAnimations(const TArray<UAnimSequence*>& Animations, const TArray<float>& Weights);

    // === Animation Analysis ===

    /**
     * Analyze animation quality
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|Animation|Analysis", CallInEditor)
    FAuracronAnimationResult AnalyzeAnimationQuality(UAnimationAsset* Animation);

    /**
     * Get animation statistics
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|Animation|Analysis", CallInEditor)
    FString GetAnimationStatistics(UAnimationAsset* Animation);

    /**
     * Validate animation for MetaHuman
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|Animation|Analysis", CallInEditor)
    FAuracronAnimationResult ValidateMetaHumanAnimation(UAnimationAsset* Animation, USkeletalMesh* MetaHumanMesh);

    // === Real-time Animation ===

    /**
     * Apply real-time facial animation
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|Animation|RealTime", CallInEditor)
    bool ApplyRealtimeFacialAnimation(USkeletalMeshComponent* MeshComponent, const TMap<FString, float>& BlendShapeWeights);

    /**
     * Update eye look target
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|Animation|RealTime", CallInEditor)
    bool UpdateEyeLookTarget(USkeletalMeshComponent* MeshComponent, const FVector& LookTarget);

    /**
     * Trigger facial expression
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|Animation|RealTime", CallInEditor)
    bool TriggerFacialExpression(USkeletalMeshComponent* MeshComponent, const FString& ExpressionName, float Intensity);

    // === Utility Functions ===

    /**
     * Initialize animation system
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|Animation|Utility", CallInEditor)
    bool Initialize(UAuracronMetaHumanFramework* InFramework);

    /**
     * Shutdown animation system
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|Animation|Utility", CallInEditor)
    void Shutdown();

    /**
     * Check if animation system is initialized
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|Animation|Utility", CallInEditor)
    bool IsInitialized() const;

    /**
     * Get supported animation types
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|Animation|Utility", CallInEditor)
    TArray<FString> GetSupportedAnimationTypes() const;

public:
    // === Events ===

    /** Called when an async animation operation completes */
    UPROPERTY(BlueprintAssignable, Category = "AURACRON MetaHuman|Animation|Events")
    FAuracronAnimationComplete OnAnimationComplete;

protected:
    // === Internal State ===

    UPROPERTY()
    TObjectPtr<UAuracronMetaHumanFramework> OwnerFramework;

    UPROPERTY()
    bool bIsInitialized = false;

    UPROPERTY()
    FString LastErrorMessage;

    // Async operation tracking
    TMap<FString, TSharedPtr<class FAuracronAnimationAsyncTask>> ActiveAsyncOperations;
    FCriticalSection AsyncOperationsMutex;

    // Animation cache
    TMap<FString, TWeakObjectPtr<UAnimationAsset>> AnimationCache;
    FCriticalSection AnimationCacheMutex;

private:
    // === Internal Methods ===

    FString GenerateOperationID() const;
    void CleanupCompletedAsyncOperations();
    bool ValidateAnimationParameters(const FAuracronAnimationParams& Params, FString& OutError) const;
    bool ValidateFacialAnimationConfig(const FAuracronFacialAnimConfig& Config, FString& OutError) const;
    UAnimSequence* CreateAnimSequenceInternal(const FAuracronAnimationParams& Params, USkeletalMesh* TargetMesh);
    bool ProcessFacialAnimationInternal(const FAuracronFacialAnimConfig& Config, UAnimSequence* AnimSequence);
    void CacheAnimation(const FString& Key, UAnimationAsset* Animation);
    UAnimationAsset* GetCachedAnimation(const FString& Key) const;
};

