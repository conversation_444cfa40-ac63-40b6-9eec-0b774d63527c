#pragma once

#include "CoreMinimal.h"
#include "Subsystems/WorldSubsystem.h"
#include "GameplayTagContainer.h"
#include "Engine/DataTable.h"
#include "AuracronHarmonyEngineBridge.h"
#include "EmotionalIntelligenceComponent.h"
#include "PositiveBehaviorPredictor.h"
#include "CommunityHealingManager.h"
#include "HarmonyRewardsSystem.h"
#include "RealTimeInterventionSystem.h"
#include "HarmonyEngineSubsystem.generated.h"

class APlayerController;
class APlayerState;
class UAbilitySystemComponent;

/**
 * Central subsystem that orchestrates all Harmony Engine functionality
 * Manages anti-toxicity AI, community healing, and positive behavior systems
 */
UCLASS(BlueprintType, Blueprintable)
class AURACRONHARMONYENGINEBRIDGE_API UHarmonyEngineSubsystem : public UWorldSubsystem
{
    GENERATED_BODY()

public:
    UHarmonyEngineSubsystem();

    // USubsystem interface
    virtual void Initialize(FSubsystemCollectionBase& Collection) override;
    virtual void Deinitialize() override;
    virtual bool ShouldCreateSubsystem(UObject* Outer) const override;

    // Core Harmony Engine Functions
    UFUNCTION(BlueprintCallable, Category = "Harmony Engine")
    void RegisterPlayer(APlayerController* PlayerController);

    UFUNCTION(BlueprintCallable, Category = "Harmony Engine")
    void UnregisterPlayer(APlayerController* PlayerController);

    UFUNCTION(BlueprintCallable, Category = "Harmony Engine")
    void UpdatePlayerBehavior(const FString& PlayerID, const FPlayerBehaviorSnapshot& BehaviorData);

    UFUNCTION(BlueprintCallable, Category = "Harmony Engine")
    void TriggerIntervention(const FString& PlayerID, EInterventionType InterventionType, const FString& Reason);

    UFUNCTION(BlueprintCallable, Category = "Harmony Engine")
    void InitiateCommunityHealing(const FString& VictimID, const FString& HealerID);

    UFUNCTION(BlueprintCallable, Category = "Harmony Engine")
    void AwardKindnessPoints(const FString& PlayerID, int32 Points, const FString& Reason);

    // Behavior Analysis
    UFUNCTION(BlueprintCallable, Category = "Harmony Engine")
    float GetPlayerToxicityScore(const FString& PlayerID) const;

    UFUNCTION(BlueprintCallable, Category = "Harmony Engine")
    float GetPlayerPositivityScore(const FString& PlayerID) const;

    UFUNCTION(BlueprintCallable, Category = "Harmony Engine")
    EEmotionalState GetPlayerEmotionalState(const FString& PlayerID) const;

    UFUNCTION(BlueprintCallable, Category = "Harmony Engine")
    bool IsPlayerAtRisk(const FString& PlayerID) const;

    // Community Management
    UFUNCTION(BlueprintCallable, Category = "Harmony Engine")
    TArray<FString> GetCommunityHeroes() const;

    UFUNCTION(BlueprintCallable, Category = "Harmony Engine")
    TArray<FString> GetAvailableMentors() const;

    UFUNCTION(BlueprintCallable, Category = "Harmony Engine")
    bool MatchMentorWithStudent(const FString& MentorID, const FString& StudentID);

    // Real-time Monitoring
    UFUNCTION(BlueprintCallable, Category = "Harmony Engine")
    void StartBehaviorMonitoring(const FString& PlayerID);

    UFUNCTION(BlueprintCallable, Category = "Harmony Engine")
    void StopBehaviorMonitoring(const FString& PlayerID);

    // Analysis methods
    void PerformPeriodicAnalysis();
    void AnalyzeBehaviorForIntervention(const FString& PlayerID, const FPlayerBehaviorSnapshot& BehaviorData);
    bool IsPlayerOnInterventionCooldown(const FString& PlayerID);
    void AnalyzePlayerBehavior(const FString& PlayerID);

    UFUNCTION(BlueprintCallable, Category = "Harmony Engine")
    float GetCommunityHealthScore() const;

    // Event Delegates
    UPROPERTY(BlueprintAssignable, Category = "Harmony Engine Events")
    FOnBehaviorDetected OnBehaviorDetected;

    UPROPERTY(BlueprintAssignable, Category = "Harmony Engine Events")
    FOnInterventionTriggered OnInterventionTriggered;

    UPROPERTY(BlueprintAssignable, Category = "Harmony Engine Events")
    FOnCommunityHealing OnCommunityHealing;

    UPROPERTY(BlueprintAssignable, Category = "Harmony Engine Events")
    FOnKindnessReward OnKindnessReward;

    UPROPERTY(BlueprintAssignable, Category = "Harmony Engine Events")
    FOnHarmonyLevelChanged OnHarmonyLevelChanged;

protected:
    // Core Components
    UPROPERTY()
    TObjectPtr<UEmotionalIntelligenceComponent> EmotionalIntelligence;

    UPROPERTY()
    TObjectPtr<UPositiveBehaviorPredictor> BehaviorPredictor;

    UPROPERTY()
    TObjectPtr<UCommunityHealingManager> HealingManager;

    UPROPERTY()
    TObjectPtr<UHarmonyRewardsSystem> RewardsSystem;

    UPROPERTY()
    TObjectPtr<URealTimeInterventionSystem> InterventionSystem;

    // Player Data Management
    UPROPERTY()
    TMap<FString, FPlayerBehaviorSnapshot> PlayerBehaviorData;

    UPROPERTY()
    TMap<FString, TObjectPtr<APlayerController>> RegisteredPlayers;

    UPROPERTY()
    TMap<FString, float> PlayerKindnessScores;

    UPROPERTY()
    TMap<FString, FDateTime> LastInterventionTimes;

    // Configuration thresholds
    UPROPERTY(EditDefaultsOnly, Category = "Harmony Engine Config")
    float ToxicityThreshold = 0.7f;

    UPROPERTY(EditDefaultsOnly, Category = "Harmony Engine Config")
    float FrustrationThreshold = 0.8f;

    UPROPERTY(EditDefaultsOnly, Category = "Harmony Engine Config")
    FTimespan InterventionCooldown = FTimespan::FromMinutes(5.0);

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Harmony Engine Config")
    int32 MaxConcurrentInterventions;

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Harmony Engine Config")
    bool bEnableRealTimeMonitoring;

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Harmony Engine Config")
    bool bEnablePredictiveIntervention;

    // Timers
    FTimerHandle BehaviorAnalysisTimer;
    FTimerHandle CommunityHealthTimer;
    FTimerHandle MLModelUpdateTimer;

private:
    // Internal helper functions
    void UpdateCommunityHealth();
    void ProcessPendingInterventions();
    void UpdateMLModels();
    void SavePlayerData();
    void LoadPlayerData();

    // Behavior pattern analysis
    bool DetectToxicityPatterns(const FPlayerBehaviorSnapshot& BehaviorData);
    bool DetectPositivePatterns(const FPlayerBehaviorSnapshot& BehaviorData);
    float CalculateEmotionalTrend(const FString& PlayerID);
    
    // Intervention logic
    EInterventionType DetermineInterventionLevel(const FPlayerBehaviorSnapshot& BehaviorData);
    void ExecuteIntervention(const FString& PlayerID, const FHarmonyInterventionData& InterventionData);
    
    // Community healing logic
    void ProcessHealingRequest(const FString& VictimID, const FString& HealerID);
    bool ValidateHealingEligibility(const FString& HealerID);
    
    // Reward calculation
    int32 CalculateKindnessReward(const FPlayerBehaviorSnapshot& BehaviorData);
    void ProcessRewardDistribution();

    // Additional helper methods
    FString GenerateInterventionMessage(EInterventionType InterventionType, const FString& Reason);
    float CalculateInterventionPriority(EInterventionType InterventionType);
    void UpdateInterventionCooldown(const FString& PlayerID);
    float CalculateExperienceMultiplier(int32 KindnessPoints);
    void SavePlayerBehaviorData(const FString& PlayerID);
    void BroadcastCommunityEvent(const FString& EventMessage);

public:
    // Getter for EmotionalIntelligence component
    UFUNCTION(BlueprintCallable, Category = "Harmony Engine")
    UEmotionalIntelligenceComponent* GetEmotionalIntelligence() const { return EmotionalIntelligence.Get(); }
};
