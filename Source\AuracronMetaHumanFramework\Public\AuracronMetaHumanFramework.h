﻿// AURACRON MetaHuman Framework - Modular MetaHuman Integration for UE 5.6
// Comprehensive MetaHuman creation, animation, and optimization system
// Author: Augment Agent
// Date: 2025-08-05
// Version: 2.0.0 - Refactored Modular Architecture

#pragma once

#include "CoreMinimal.h"
#include "Modules/ModuleManager.h"
#include "Engine/Engine.h"
#include "UObject/Object.h"
#include "UObject/ObjectMacros.h"
#include "Logging/LogMacros.h"

// MetaHuman SDK includes
#if WITH_METAHUMAN_DNA_CALIBRATION
// Forward declarations for MetaHuman DNA compatibility
class FDNAReader;
class FDNAWriter;
class FDNACalibration;
class FRigLogic;
#endif

// Forward declarations
class UAuracronMetaHumanDNASystem;
class UAuracronMetaHumanAnimationSystem;
class UAuracronMetaHumanTextureSystem;
class UAuracronMetaHumanPerformanceSystem;
class UAuracronMetaHumanClothingSystem;
class UAuracronMetaHumanPythonBindings;

DECLARE_LOG_CATEGORY_EXTERN(LogAuracronMetaHumanFramework, Log, All);

/**
 * MetaHuman Framework Quality Levels
 */
UENUM(BlueprintType)
enum class EAuracronMetaHumanQuality : uint8
{
    Low         UMETA(DisplayName = "Low Quality"),
    Medium      UMETA(DisplayName = "Medium Quality"),
    High        UMETA(DisplayName = "High Quality"),
    Cinematic   UMETA(DisplayName = "Cinematic Quality"),
    Custom      UMETA(DisplayName = "Custom Quality")
};

/**
 * MetaHuman Framework Processing Priority
 */
UENUM(BlueprintType)
enum class EAuracronMetaHumanPriority : uint8
{
    Background  UMETA(DisplayName = "Background"),
    Low         UMETA(DisplayName = "Low"),
    Normal      UMETA(DisplayName = "Normal"),
    High        UMETA(DisplayName = "High"),
    Critical    UMETA(DisplayName = "Critical")
};

/**
 * MetaHuman Framework Configuration
 */
USTRUCT(BlueprintType)
struct AURACRONMETAHUMANFRAMEWORK_API FAuracronMetaHumanConfig
{
    GENERATED_BODY()

    /** Enable GPU acceleration for processing */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    bool bEnableGPUAcceleration = true;

    /** Enable multi-threading for operations */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    bool bEnableMultiThreading = true;

    /** Maximum number of worker threads */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance", meta = (ClampMin = "1", ClampMax = "32"))
    int32 MaxWorkerThreads = 8;

    /** Memory pool size in MB */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Memory", meta = (ClampMin = "64", ClampMax = "8192"))
    int32 MemoryPoolSizeMB = 512;

    /** Enable detailed logging */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Debug")
    bool bEnableDetailedLogging = false;

    /** Enable performance profiling */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Debug")
    bool bEnablePerformanceProfiling = true;

    /** Default quality level */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Quality")
    EAuracronMetaHumanQuality DefaultQuality = EAuracronMetaHumanQuality::High;

    /** Default processing priority */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Processing")
    EAuracronMetaHumanPriority DefaultPriority = EAuracronMetaHumanPriority::Normal;

    FAuracronMetaHumanConfig()
    {
        // Initialize with optimal defaults for UE 5.6
    }
};

/**
 * MetaHuman Framework Performance Metrics
 */
USTRUCT(BlueprintType)
struct AURACRONMETAHUMANFRAMEWORK_API FAuracronMetaHumanMetrics
{
    GENERATED_BODY()

    /** Total processing time in milliseconds */
    UPROPERTY(BlueprintReadOnly, Category = "Performance")
    float TotalProcessingTimeMS = 0.0f;

    /** Memory usage in MB */
    UPROPERTY(BlueprintReadOnly, Category = "Memory")
    float MemoryUsageMB = 0.0f;

    /** GPU usage percentage */
    UPROPERTY(BlueprintReadOnly, Category = "GPU")
    float GPUUsagePercent = 0.0f;

    /** Number of active tasks */
    UPROPERTY(BlueprintReadOnly, Category = "Tasks")
    int32 ActiveTasks = 0;

    /** Number of completed tasks */
    UPROPERTY(BlueprintReadOnly, Category = "Tasks")
    int32 CompletedTasks = 0;

    /** Cache hit ratio */
    UPROPERTY(BlueprintReadOnly, Category = "Cache")
    float CacheHitRatio = 0.0f;

    /** Last update timestamp */
    UPROPERTY(BlueprintReadOnly, Category = "Timing")
    FDateTime LastUpdateTime;

    FAuracronMetaHumanMetrics()
    {
        LastUpdateTime = FDateTime::Now();
    }
};

/**
 * Main AURACRON MetaHuman Framework Class
 * Provides modular MetaHuman integration with UE 5.6 latest APIs
 */
UCLASS(BlueprintType, Blueprintable, Category = "AURACRON|MetaHuman", meta = (DisplayName = "AURACRON MetaHuman Framework"))
class AURACRONMETAHUMANFRAMEWORK_API UAuracronMetaHumanFramework : public UObject
{
    GENERATED_BODY()

public:
    UAuracronMetaHumanFramework();
    virtual ~UAuracronMetaHumanFramework();

    // === Core Framework Management ===

    /**
     * Initialize the MetaHuman Framework with configuration
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|Core", CallInEditor)
    bool InitializeFramework(const FAuracronMetaHumanConfig& Config);

    /**
     * Shutdown the MetaHuman Framework
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|Core", CallInEditor)
    void ShutdownFramework();

    /**
     * Check if framework is initialized
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|Core", CallInEditor)
    bool IsFrameworkInitialized() const;

    /**
     * Get current framework configuration
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|Core", CallInEditor)
    FAuracronMetaHumanConfig GetFrameworkConfiguration() const;

    /**
     * Update framework configuration
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|Core", CallInEditor)
    bool UpdateFrameworkConfiguration(const FAuracronMetaHumanConfig& NewConfig);

    // === System Access ===

    /**
     * Get DNA System instance
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|Systems", CallInEditor)
    UAuracronMetaHumanDNASystem* GetDNASystem() const;

    /**
     * Get Animation System instance
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|Systems", CallInEditor)
    UAuracronMetaHumanAnimationSystem* GetAnimationSystem() const;

    /**
     * Get Texture System instance
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|Systems", CallInEditor)
    UAuracronMetaHumanTextureSystem* GetTextureSystem() const;

    /**
     * Get Performance System instance
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|Systems", CallInEditor)
    UAuracronMetaHumanPerformanceSystem* GetPerformanceSystem() const;

    /**
     * Get Clothing System instance
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|Systems", CallInEditor)
    UAuracronMetaHumanClothingSystem* GetClothingSystem() const;

    /**
     * Get Python Bindings instance
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|Systems", CallInEditor)
    UAuracronMetaHumanPythonBindings* GetPythonBindings() const;

    // === Performance Monitoring ===

    /**
     * Get current performance metrics
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|Performance", CallInEditor)
    FAuracronMetaHumanMetrics GetPerformanceMetrics() const;

    /**
     * Update performance metrics
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|Performance", CallInEditor)
    void UpdatePerformanceMetrics();

    /**
     * Reset performance metrics
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|Performance", CallInEditor)
    void ResetPerformanceMetrics();

    // === Utility Functions ===

    /**
     * Get framework version
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|Utility", CallInEditor)
    FString GetFrameworkVersion() const;

    /**
     * Get supported MetaHuman SDK version
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|Utility", CallInEditor)
    FString GetSupportedSDKVersion() const;

    /**
     * Validate system requirements
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|Utility", CallInEditor)
    bool ValidateSystemRequirements(FString& OutErrorMessage) const;

protected:
    // === Internal Systems ===
    
    UPROPERTY()
    TObjectPtr<UAuracronMetaHumanDNASystem> DNASystem;
    
    UPROPERTY()
    TObjectPtr<UAuracronMetaHumanAnimationSystem> AnimationSystem;
    
    UPROPERTY()
    TObjectPtr<UAuracronMetaHumanTextureSystem> TextureSystem;
    
    UPROPERTY()
    TObjectPtr<UAuracronMetaHumanPerformanceSystem> PerformanceSystem;
    
    UPROPERTY()
    TObjectPtr<UAuracronMetaHumanClothingSystem> ClothingSystem;
    
    UPROPERTY()
    TObjectPtr<UAuracronMetaHumanPythonBindings> PythonBindings;

    // === Configuration and State ===
    
    UPROPERTY()
    FAuracronMetaHumanConfig CurrentConfig;
    
    UPROPERTY()
    FAuracronMetaHumanMetrics CurrentMetrics;
    
    UPROPERTY()
    bool bIsInitialized = false;

private:
    // === Internal Methods ===
    
    bool InitializeSystems();
    void ShutdownSystems();
    bool ValidateConfiguration(const FAuracronMetaHumanConfig& Config, FString& OutError) const;
    void UpdateSystemConfigurations();
};

/**
 * AURACRON MetaHuman Framework Module
 */
class FAuracronMetaHumanFrameworkModule : public IModuleInterface
{
public:
    /** IModuleInterface implementation */
    virtual void StartupModule() override;
    virtual void ShutdownModule() override;
    
    /** Get the singleton framework instance */
    static UAuracronMetaHumanFramework* GetFramework();

private:
    static TObjectPtr<UAuracronMetaHumanFramework> FrameworkInstance;
};

#include "AuracronMetaHumanFramework.generated.h"

