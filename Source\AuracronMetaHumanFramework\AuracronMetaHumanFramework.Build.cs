﻿using UnrealBuildTool;
public class AuracronMetaHumanFramework : ModuleRules
{
    public AuracronMetaHumanFramework(ReadOnlyTargetRules Target) : base(Target)
    {
        PCHUsage = PCHUsageMode.UseExplicitOrSharedPCHs;
        PublicDependencyModuleNames.AddRange(new string[] 
        {
            "Core",
            "CoreUObject",
            "Engine",
            "RenderCore",
            "RHI","Slate",
            "SlateCore",
            "UMG",
            "RigLogicModule","RigLogicLib",
            "ControlRig",
            "AnimationCore",
            "AnimGraphRuntime",
            "SkeletalMeshUtilitiesCommon",
            "MeshDescription",
            "StaticMeshDescription",
            "GeometryCore",
            "DynamicMesh",
            "GeometryFramework",
            "InteractiveToolsFramework",
            "ModelingComponents",
            "ModelingOperators",
            "MeshConversion"
        });
        PrivateDependencyModuleNames.AddRange(
            new string[]
            {
                "<PERSON><PERSON>",
                "<PERSON><PERSON>",
                "InputCore",
                "ImageWrapper",
                "AutomationController",
                "AutomationMessages",
                "AutomationTest",
                "AutomationWorker",
                "FunctionalTesting",
                "ScreenShotComparisonTools",
                "SessionServices",
                "GameplayDebugger",
                "EngineSettings",
                "DeveloperSettings",
                "ClothingSystemRuntimeInterface",
                "ClothingSystemRuntimeCommon",
                "PhysicsCore",
                "ChaosCore",
                "NiagaraCore",
                "NiagaraShader",
                "VectorVM",
                "AudioMixer",
                "SignalProcessing",
                "SoundFieldRendering",
                "AudioExtensions",
                "MetasoundFrontend",
                "MetasoundGenerator",
                "MetasoundStandardNodes",
                "WaveTable",
                "Synthesis",
                "AudioSynesthesia",
                "AudioAnalyzer"
            }
        );
        
        // Editor-only dependencies
        if (Target.Type == TargetType.Editor)
        {
            PublicDependencyModuleNames.AddRange(
                new string[]
                {
                    "ToolMenus",
                    "EditorStyle",
                    "EditorWidgets",
                    "UnrealEd",
                    "LevelEditor",
                    "PropertyEditor",
                    "DetailCustomizations",
                    "ComponentVisualizers",
                    "ControlRigEditor",
                    "AnimGraph",
                    "BlueprintGraph",
                    "KismetCompiler",
                    "EditorInteractiveToolsFramework"
                }
            );

            PrivateDependencyModuleNames.AddRange(
                new string[]
                {
                    "MetasoundGraphCore"
                }
            );
        }
        if (Target.bBuildEditor)
        {
            PrivateDependencyModuleNames.AddRange(new string[]
            {
                "UnrealEd",
                "ToolMenus",
                "EditorStyle",
                "EditorWidgets",
                "PropertyEditor",
                "DetailCustomizations",
                "ComponentVisualizers",
                "Persona",
                "SkeletalMeshEditor",
                "AnimationBlueprintEditor",
                "AnimationEditor",
                "SequencerCore",
                "Sequencer",
                "MovieScene",
                "MovieSceneTools",
                "MovieSceneTracks","LevelSequenceEditor",
                "CinematicCamera",
                "TakeRecorder",
                "TakesCore",
                "LiveLinkInterface",
                "LiveLinkComponents",
                "LiveLinkEditor",
                "LiveLinkAnimationCore",
                "LiveLinkMovieScene",
                "RemoteControl",
                "RemoteControlUI",
                "RemoteControlLogic",
                "RemoteControlProtocol",
                "RemoteControlProtocolWidgets",
                "VirtualCamera","VCamExtensions"
            });
        }
        // Enable RTTI for this module
        bUseRTTI = true;
        // Enable exceptions for this module
        bEnableExceptions = true;
        // Optimization settings
        OptimizeCode = CodeOptimization.InShippingBuildsOnly;
        // Include paths
        PublicIncludePaths.AddRange(new string[] 
        {
            "AuracronMetaHumanFramework/Public"
        });
        PrivateIncludePaths.AddRange(new string[] 
        {
            "AuracronMetaHumanFramework/Private"
        });
        // MetaHuman DNA Calibration support
        PublicDefinitions.Add("WITH_METAHUMAN_DNA_CALIBRATION=1");
        // Platform-specific configurations
        if (Target.Platform == UnrealTargetPlatform.Win64)
        {
            PublicDefinitions.Add("PLATFORM_WINDOWS=1");
        }
        else if (Target.Platform == UnrealTargetPlatform.Mac)
        {
            PublicDefinitions.Add("PLATFORM_MAC=1");
        }
        else if (Target.Platform == UnrealTargetPlatform.Linux)
        {
            PublicDefinitions.Add("PLATFORM_LINUX=1");
        }
        // Performance optimizations
        PublicDefinitions.Add("AURACRON_METAHUMAN_OPTIMIZED=1");
        PublicDefinitions.Add("AURACRON_METAHUMAN_THREADING=1");
        PublicDefinitions.Add("AURACRON_METAHUMAN_GPU_ACCELERATION=1");
    }
}


