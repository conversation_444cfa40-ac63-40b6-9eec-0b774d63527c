// AURACRON MetaHuman Texture System - Advanced Texture Processing for UE 5.6
// Handles MetaHuman texture generation, optimization, and real-time processing
// Author: Augment Agent
// Date: 2025-08-05
// Version: 2.0.0

#pragma once

#include "CoreMinimal.h"
#include "UObject/Object.h"
#include "Engine/Engine.h"
#include "Engine/Texture2D.h"
#include "Engine/TextureRenderTarget2D.h"
#include "Materials/Material.h"
#include "Materials/MaterialInstance.h"
#include "Materials/MaterialInstanceDynamic.h"
#include "Materials/MaterialParameterCollection.h"
#include "RenderCore.h"
#include "RHI.h"
#include "RHICommandList.h"
#include "GlobalShader.h"
#include "ShaderParameterStruct.h"
#include "Async/AsyncWork.h"
#include "ImageUtils.h"

#include "AuracronMetaHumanTextureSystem.generated.h"

DECLARE_LOG_CATEGORY_EXTERN(LogAuracronMetaHumanTexture, Log, All);

// Forward declarations
class UAuracronMetaHumanFramework;

/**
 * Texture Processing Types
 */
UENUM(BlueprintType)
enum class EAuracronMetaHumanTextureType : uint8
{
    Diffuse             UMETA(DisplayName = "Diffuse/Albedo"),
    Normal              UMETA(DisplayName = "Normal Map"),
    Roughness           UMETA(DisplayName = "Roughness"),
    Metallic            UMETA(DisplayName = "Metallic"),
    Specular            UMETA(DisplayName = "Specular"),
    Emissive            UMETA(DisplayName = "Emissive"),
    Opacity             UMETA(DisplayName = "Opacity"),
    SubsurfaceColor     UMETA(DisplayName = "Subsurface Color"),
    SubsurfaceProfile   UMETA(DisplayName = "Subsurface Profile"),
    Displacement        UMETA(DisplayName = "Displacement"),
    AmbientOcclusion    UMETA(DisplayName = "Ambient Occlusion"),
    Cavity              UMETA(DisplayName = "Cavity"),
    Curvature           UMETA(DisplayName = "Curvature"),
    Thickness           UMETA(DisplayName = "Thickness"),
    WorldPositionOffset UMETA(DisplayName = "World Position Offset"),
    Custom              UMETA(DisplayName = "Custom")
};

/**
 * Texture Quality Levels
 */
UENUM(BlueprintType)
enum class EAuracronTextureQuality : uint8
{
    Low                 UMETA(DisplayName = "Low (512x512)"),
    Medium              UMETA(DisplayName = "Medium (1024x1024)"),
    High                UMETA(DisplayName = "High (2048x2048)"),
    Ultra               UMETA(DisplayName = "Ultra (4096x4096)"),
    Custom              UMETA(DisplayName = "Custom Resolution")
};

/**
 * Texture Generation Parameters
 */
USTRUCT(BlueprintType)
struct AURACRONMETAHUMANFRAMEWORK_API FAuracronTextureParams
{
    GENERATED_BODY()

    /** Texture type to generate */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Texture")
    EAuracronMetaHumanTextureType TextureType = EAuracronMetaHumanTextureType::Diffuse;

    /** Quality level */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Texture")
    EAuracronTextureQuality Quality = EAuracronTextureQuality::High;

    /** Custom resolution (if Quality is Custom) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Texture", meta = (EditCondition = "Quality == EAuracronTextureQuality::Custom"))
    FIntPoint CustomResolution = FIntPoint(2048, 2048);

    /** Enable GPU acceleration */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Texture")
    bool bUseGPUAcceleration = true;

    /** Enable texture streaming */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Texture")
    bool bEnableStreaming = true;

    /** Compression settings */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Texture")
    FString CompressionSettings = TEXT("TC_Default");

    /** Mip generation settings */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Texture")
    bool bGenerateMips = true;

    /** SRGB color space */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Texture")
    bool bSRGB = true;

    /** Additional parameters as JSON */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Texture")
    FString AdditionalParams;

    FAuracronTextureParams()
    {
        // Default constructor
    }
};

/**
 * Texture Processing Result
 */
USTRUCT(BlueprintType)
struct AURACRONMETAHUMANFRAMEWORK_API FAuracronTextureResult
{
    GENERATED_BODY()

    /** Whether the operation was successful */
    UPROPERTY(BlueprintReadOnly, Category = "Result")
    bool bSuccess = false;

    /** Result message */
    UPROPERTY(BlueprintReadOnly, Category = "Result")
    FString Message;

    /** Generated texture */
    UPROPERTY(BlueprintReadOnly, Category = "Result")
    TObjectPtr<UTexture2D> GeneratedTexture;

    /** Processing time in milliseconds */
    UPROPERTY(BlueprintReadOnly, Category = "Result")
    float ProcessingTimeMS = 0.0f;

    /** Output file path */
    UPROPERTY(BlueprintReadOnly, Category = "Result")
    FString OutputPath;

    /** Texture statistics */
    UPROPERTY(BlueprintReadOnly, Category = "Result")
    FString TextureStats;

    FAuracronTextureResult()
    {
        // Default constructor
    }

    FAuracronTextureResult(bool bInSuccess, const FString& InMessage)
        : bSuccess(bInSuccess)
        , Message(InMessage)
    {
    }
};

/**
 * Skin Texture Configuration
 */
USTRUCT(BlueprintType)
struct AURACRONMETAHUMANFRAMEWORK_API FAuracronSkinTextureConfig
{
    GENERATED_BODY()

    /** Skin tone (0.0 = very light, 1.0 = very dark) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Skin", meta = (ClampMin = "0.0", ClampMax = "1.0"))
    float SkinTone = 0.5f;

    /** Skin roughness */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Skin", meta = (ClampMin = "0.0", ClampMax = "1.0"))
    float SkinRoughness = 0.6f;

    /** Subsurface scattering intensity */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Skin", meta = (ClampMin = "0.0", ClampMax = "2.0"))
    float SubsurfaceIntensity = 1.0f;

    /** Pore detail intensity */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Skin", meta = (ClampMin = "0.0", ClampMax = "2.0"))
    float PoreDetailIntensity = 1.0f;

    /** Wrinkle intensity */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Skin", meta = (ClampMin = "0.0", ClampMax = "2.0"))
    float WrinkleIntensity = 0.5f;

    /** Age factor (0.0 = young, 1.0 = old) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Skin", meta = (ClampMin = "0.0", ClampMax = "1.0"))
    float AgeFactor = 0.3f;

    /** Enable freckles */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Skin")
    bool bEnableFreckles = false;

    /** Freckle intensity */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Skin", meta = (ClampMin = "0.0", ClampMax = "1.0", EditCondition = "bEnableFreckles"))
    float FreckleIntensity = 0.3f;

    FAuracronSkinTextureConfig()
    {
        // Default constructor
    }
};

DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FAuracronTextureComplete, const FAuracronTextureResult&, Result, const FString&, OperationID);

/**
 * AURACRON MetaHuman Texture System
 * Advanced texture processing and generation system for UE 5.6
 */
UCLASS(BlueprintType, Blueprintable, Category = "AURACRON|MetaHuman|Texture")
class AURACRONMETAHUMANFRAMEWORK_API UAuracronMetaHumanTextureSystem : public UObject
{
    GENERATED_BODY()

public:
    UAuracronMetaHumanTextureSystem();
    virtual ~UAuracronMetaHumanTextureSystem();

    // === Core Texture Operations ===

    /**
     * Generate texture from parameters
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|Texture|Core", CallInEditor)
    FAuracronTextureResult GenerateTexture(const FAuracronTextureParams& Params);

    /**
     * Generate skin texture
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|Texture|Core", CallInEditor)
    FAuracronTextureResult GenerateSkinTexture(const FAuracronSkinTextureConfig& Config, const FAuracronTextureParams& Params);

    /**
     * Optimize texture
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|Texture|Core", CallInEditor)
    FAuracronTextureResult OptimizeTexture(UTexture2D* Texture);

    /**
     * Convert texture format
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|Texture|Core", CallInEditor)
    FAuracronTextureResult ConvertTextureFormat(UTexture2D* SourceTexture, const FString& TargetFormat);

    // === Async Texture Operations ===

    /**
     * Generate texture asynchronously
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|Texture|Async", CallInEditor)
    FString GenerateTextureAsync(const FAuracronTextureParams& Params);

    /**
     * Generate skin texture asynchronously
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|Texture|Async", CallInEditor)
    FString GenerateSkinTextureAsync(const FAuracronSkinTextureConfig& Config, const FAuracronTextureParams& Params);

    /**
     * Check if async operation is complete
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|Texture|Async", CallInEditor)
    bool IsAsyncOperationComplete(const FString& OperationID) const;

    /**
     * Get async operation result
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|Texture|Async", CallInEditor)
    FAuracronTextureResult GetAsyncOperationResult(const FString& OperationID);

    // === GPU Compute Operations ===

    /**
     * Process texture with GPU compute shader
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|Texture|GPU", CallInEditor)
    FAuracronTextureResult ProcessTextureWithGPU(UTexture2D* InputTexture, const FString& ShaderName, const TMap<FString, float>& Parameters);

    /**
     * Blend textures using GPU
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|Texture|GPU", CallInEditor)
    FAuracronTextureResult BlendTexturesGPU(const TArray<UTexture2D*>& InputTextures, const TArray<float>& BlendWeights);

    /**
     * Apply texture filter using GPU
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|Texture|GPU", CallInEditor)
    FAuracronTextureResult ApplyTextureFilterGPU(UTexture2D* InputTexture, const FString& FilterType, float FilterStrength);

    // === Texture Analysis ===

    /**
     * Analyze texture quality
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|Texture|Analysis", CallInEditor)
    FAuracronTextureResult AnalyzeTextureQuality(UTexture2D* Texture);

    /**
     * Get texture statistics
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|Texture|Analysis", CallInEditor)
    FString GetTextureStatistics(UTexture2D* Texture);

    /**
     * Validate texture for MetaHuman
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|Texture|Analysis", CallInEditor)
    FAuracronTextureResult ValidateMetaHumanTexture(UTexture2D* Texture, EAuracronMetaHumanTextureType ExpectedType);

    // === Real-time Texture Operations ===

    /**
     * Update dynamic material parameter
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|Texture|RealTime", CallInEditor)
    bool UpdateDynamicMaterialParameter(UMaterialInstanceDynamic* MaterialInstance, const FString& ParameterName, float Value);

    /**
     * Update dynamic texture parameter
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|Texture|RealTime", CallInEditor)
    bool UpdateDynamicTextureParameter(UMaterialInstanceDynamic* MaterialInstance, const FString& ParameterName, UTexture* Texture);

    /**
     * Create render target texture
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|Texture|RealTime", CallInEditor)
    UTextureRenderTarget2D* CreateRenderTargetTexture(int32 Width, int32 Height, EPixelFormat Format);

    // === Utility Functions ===

    /**
     * Initialize texture system
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|Texture|Utility", CallInEditor)
    bool Initialize(UAuracronMetaHumanFramework* InFramework);

    /**
     * Shutdown texture system
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|Texture|Utility", CallInEditor)
    void Shutdown();

    /**
     * Check if texture system is initialized
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|Texture|Utility", CallInEditor)
    bool IsInitialized() const;

    /**
     * Get supported texture formats
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|Texture|Utility", CallInEditor)
    TArray<FString> GetSupportedTextureFormats() const;

public:
    // === Events ===

    /** Called when an async texture operation completes */
    UPROPERTY(BlueprintAssignable, Category = "AURACRON MetaHuman|Texture|Events")
    FAuracronTextureComplete OnTextureComplete;

protected:
    // === Internal State ===

    UPROPERTY()
    TObjectPtr<UAuracronMetaHumanFramework> OwnerFramework;

    UPROPERTY()
    bool bIsInitialized = false;

    UPROPERTY()
    FString LastErrorMessage;

    // Async operation tracking
    TMap<FString, TSharedPtr<class FAuracronTextureAsyncTask>> ActiveAsyncOperations;
    FCriticalSection AsyncOperationsMutex;

    // Texture cache
    TMap<FString, TWeakObjectPtr<UTexture2D>> TextureCache;
    FCriticalSection TextureCacheMutex;

    // GPU compute shaders
    TMap<FString, TSharedPtr<class FGlobalShader>> ComputeShaders;

private:
    // === Internal Methods ===

    FString GenerateOperationID() const;
    void CleanupCompletedAsyncOperations();
    bool ValidateTextureParameters(const FAuracronTextureParams& Params, FString& OutError) const;
    bool ValidateSkinTextureConfig(const FAuracronSkinTextureConfig& Config, FString& OutError) const;
    UTexture2D* CreateTextureInternal(const FAuracronTextureParams& Params);
    bool ProcessSkinTextureInternal(const FAuracronSkinTextureConfig& Config, UTexture2D* Texture);
    void CacheTexture(const FString& Key, UTexture2D* Texture);
    UTexture2D* GetCachedTexture(const FString& Key) const;
    bool InitializeGPUComputeShaders();
    void ShutdownGPUComputeShaders();
    FIntPoint GetResolutionFromQuality(EAuracronTextureQuality Quality, const FIntPoint& CustomResolution) const;
};

