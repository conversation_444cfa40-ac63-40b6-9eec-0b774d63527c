// AURACRON MetaHuman DNA System - Advanced DNA Processing for UE 5.6
// Handles MetaHuman DNA calibration, manipulation, and optimization
// Author: Augment Agent
// Date: 2025-08-05
// Version: 2.0.0

#pragma once

#include "CoreMinimal.h"
#include "UObject/Object.h"
#include "Engine/Engine.h"
#include "Async/AsyncWork.h"
#include "HAL/ThreadSafeBool.h"

// MetaHuman DNA includes
#if WITH_METAHUMAN_DNA_CALIBRATION
#include "dna/BinaryStreamReader.h"
#include "dna/BinaryStreamWriter.h"
// Note: DNACalib is not supported in UE 5.6, using forward declaration instead
// #include "dnacalib/DNACalibDNAReader.h"
#include "dna/DataLayer.h"
#include "dna/Status.h"
// Forward declarations for MetaHuman DNA compatibility
class FRigLogic;
namespace dnacalib { class DNACalibDNAReader; }
#endif

#include "AuracronMetaHumanDNASystem.generated.h"

DECLARE_LOG_CATEGORY_EXTERN(LogAuracronMetaHumanDNA, Log, All);

// Forward declarations
class UAuracronMetaHumanFramework;

/**
 * DNA Processing Operation Types
 */
UENUM(BlueprintType)
enum class EAuracronDNAOperation : uint8
{
    Load                UMETA(DisplayName = "Load DNA"),
    Save                UMETA(DisplayName = "Save DNA"),
    Validate            UMETA(DisplayName = "Validate DNA"),
    Optimize            UMETA(DisplayName = "Optimize DNA"),
    Merge               UMETA(DisplayName = "Merge DNA"),
    Extract             UMETA(DisplayName = "Extract DNA"),
    Calibrate           UMETA(DisplayName = "Calibrate DNA"),
    Convert             UMETA(DisplayName = "Convert DNA")
};

/**
 * DNA Processing Result
 */
USTRUCT(BlueprintType)
struct AURACRONMETAHUMANFRAMEWORK_API FAuracronDNAResult
{
    GENERATED_BODY()

    /** Operation that was performed */
    UPROPERTY(BlueprintReadOnly, Category = "Result")
    EAuracronDNAOperation Operation = EAuracronDNAOperation::Load;

    /** Whether the operation was successful */
    UPROPERTY(BlueprintReadOnly, Category = "Result")
    bool bSuccess = false;

    /** Result message */
    UPROPERTY(BlueprintReadOnly, Category = "Result")
    FString Message;

    /** Processing time in milliseconds */
    UPROPERTY(BlueprintReadOnly, Category = "Result")
    float ProcessingTimeMS = 0.0f;

    /** Output file path (if applicable) */
    UPROPERTY(BlueprintReadOnly, Category = "Result")
    FString OutputPath;

    /** Additional data as JSON string */
    UPROPERTY(BlueprintReadOnly, Category = "Result")
    FString AdditionalData;

    FAuracronDNAResult()
    {
        // Default constructor
    }

    FAuracronDNAResult(EAuracronDNAOperation InOperation, bool bInSuccess, const FString& InMessage)
        : Operation(InOperation)
        , bSuccess(bInSuccess)
        , Message(InMessage)
    {
    }
};

/**
 * DNA Metadata Structure
 */
USTRUCT(BlueprintType)
struct AURACRONMETAHUMANFRAMEWORK_API FAuracronDNAMetadata
{
    GENERATED_BODY()

    /** DNA file name */
    UPROPERTY(BlueprintReadOnly, Category = "Metadata")
    FString FileName;

    /** DNA version */
    UPROPERTY(BlueprintReadOnly, Category = "Metadata")
    FString Version;

    /** DNA type */
    UPROPERTY(BlueprintReadOnly, Category = "Metadata")
    FString Type;

    /** Creation timestamp */
    UPROPERTY(BlueprintReadOnly, Category = "Metadata")
    FDateTime CreationTime;

    /** File size in bytes */
    UPROPERTY(BlueprintReadOnly, Category = "Metadata")
    int64 FileSize = 0;

    /** Number of meshes */
    UPROPERTY(BlueprintReadOnly, Category = "Metadata")
    int32 MeshCount = 0;

    /** Number of joints */
    UPROPERTY(BlueprintReadOnly, Category = "Metadata")
    int32 JointCount = 0;

    /** Number of blend shapes */
    UPROPERTY(BlueprintReadOnly, Category = "Metadata")
    int32 BlendShapeCount = 0;

    /** Additional metadata as JSON */
    UPROPERTY(BlueprintReadOnly, Category = "Metadata")
    FString AdditionalData;

    FAuracronDNAMetadata()
    {
        CreationTime = FDateTime::Now();
    }
};

/**
 * DNA Blend Shape Configuration
 */
USTRUCT(BlueprintType)
struct AURACRONMETAHUMANFRAMEWORK_API FAuracronDNABlendShapeConfig
{
    GENERATED_BODY()

    /** Mesh index */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Blend Shape")
    int32 MeshIndex = 0;

    /** Target index */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Blend Shape")
    int32 TargetIndex = 0;

    /** Vertex deltas */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Blend Shape")
    TArray<FVector> VertexDeltas;

    /** Blend shape name */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Blend Shape")
    FString BlendShapeName;

    /** Weight multiplier */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Blend Shape", meta = (ClampMin = "0.0", ClampMax = "2.0"))
    float WeightMultiplier = 1.0f;

    FAuracronDNABlendShapeConfig()
    {
        // Default constructor
    }
};

/**
 * DNA Joint Configuration
 */
USTRUCT(BlueprintType)
struct AURACRONMETAHUMANFRAMEWORK_API FAuracronDNAJointConfig
{
    GENERATED_BODY()

    /** Joint index */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Joint")
    int32 JointIndex = 0;

    /** Joint name */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Joint")
    FString JointName;

    /** Neutral translation */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Joint")
    FVector NeutralTranslation = FVector::ZeroVector;

    /** Neutral rotation */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Joint")
    FRotator NeutralRotation = FRotator::ZeroRotator;

    /** Parent joint index */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Joint")
    int32 ParentJointIndex = -1;

    FAuracronDNAJointConfig()
    {
        // Default constructor
    }
};

DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FAuracronDNAOperationComplete, const FAuracronDNAResult&, Result, const FString&, OperationID);

/**
 * AURACRON MetaHuman DNA System
 * Advanced DNA processing and manipulation system for UE 5.6
 */
UCLASS(BlueprintType, Blueprintable, Category = "AURACRON|MetaHuman|DNA")
class AURACRONMETAHUMANFRAMEWORK_API UAuracronMetaHumanDNASystem : public UObject
{
    GENERATED_BODY()

public:
    UAuracronMetaHumanDNASystem();
    virtual ~UAuracronMetaHumanDNASystem();

    // === Core DNA Operations ===

    /**
     * Load DNA from file
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|DNA|Core", CallInEditor)
    FAuracronDNAResult LoadDNAFromFile(const FString& FilePath);

    /**
     * Save DNA to file
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|DNA|Core", CallInEditor)
    FAuracronDNAResult SaveDNAToFile(const FString& FilePath);

    /**
     * Validate DNA data
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|DNA|Core", CallInEditor)
    FAuracronDNAResult ValidateDNA();

    /**
     * Optimize DNA data
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|DNA|Core", CallInEditor)
    FAuracronDNAResult OptimizeDNA();

    // === Async DNA Operations ===

    /**
     * Load DNA from file asynchronously
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|DNA|Async", CallInEditor)
    FString LoadDNAFromFileAsync(const FString& FilePath);

    /**
     * Save DNA to file asynchronously
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|DNA|Async", CallInEditor)
    FString SaveDNAToFileAsync(const FString& FilePath);

    /**
     * Check if async operation is complete
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|DNA|Async", CallInEditor)
    bool IsAsyncOperationComplete(const FString& OperationID) const;

    /**
     * Get async operation result
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|DNA|Async", CallInEditor)
    FAuracronDNAResult GetAsyncOperationResult(const FString& OperationID);

    // === Blend Shape Operations ===

    /**
     * Set blend shape target deltas
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|DNA|BlendShapes", CallInEditor)
    FAuracronDNAResult SetBlendShapeTargetDeltas(const FAuracronDNABlendShapeConfig& Config);

    /**
     * Get blend shape target deltas
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|DNA|BlendShapes", CallInEditor)
    FAuracronDNAResult GetBlendShapeTargetDeltas(int32 MeshIndex, int32 TargetIndex, TArray<FVector>& OutDeltas);

    /**
     * Get blend shape count for mesh
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|DNA|BlendShapes", CallInEditor)
    int32 GetBlendShapeCount(int32 MeshIndex) const;

    /**
     * Get blend shape name
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|DNA|BlendShapes", CallInEditor)
    FString GetBlendShapeName(int32 MeshIndex, int32 TargetIndex) const;

    // === Joint Operations ===

    /**
     * Set neutral joint translations
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|DNA|Joints", CallInEditor)
    FAuracronDNAResult SetNeutralJointTranslations(const TArray<FVector>& Translations);

    /**
     * Set neutral joint rotations
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|DNA|Joints", CallInEditor)
    FAuracronDNAResult SetNeutralJointRotations(const TArray<FRotator>& Rotations);

    /**
     * Get joint count
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|DNA|Joints", CallInEditor)
    int32 GetJointCount() const;

    /**
     * Get joint name
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|DNA|Joints", CallInEditor)
    FString GetJointName(int32 JointIndex) const;

    /**
     * Get joint parent index
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|DNA|Joints", CallInEditor)
    int32 GetJointParentIndex(int32 JointIndex) const;

    // === Mesh Operations ===

    /**
     * Get mesh count
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|DNA|Meshes", CallInEditor)
    int32 GetMeshCount() const;

    /**
     * Get mesh name
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|DNA|Meshes", CallInEditor)
    FString GetMeshName(int32 MeshIndex) const;

    /**
     * Get vertex count for mesh
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|DNA|Meshes", CallInEditor)
    int32 GetVertexCount(int32 MeshIndex) const;

    // === Utility Functions ===

    /**
     * Initialize DNA system
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|DNA|Utility", CallInEditor)
    bool Initialize(UAuracronMetaHumanFramework* InFramework);

    /**
     * Shutdown DNA system
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|DNA|Utility", CallInEditor)
    void Shutdown();

    /**
     * Check if DNA system is initialized
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|DNA|Utility", CallInEditor)
    bool IsInitialized() const;

    /**
     * Get DNA system status
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON MetaHuman|DNA|Utility", CallInEditor)
    FString GetSystemStatus() const;

public:
    // === Events ===

    /** Called when an async DNA operation completes */
    UPROPERTY(BlueprintAssignable, Category = "AURACRON MetaHuman|DNA|Events")
    FAuracronDNAOperationComplete OnDNAOperationComplete;

protected:
    // === Internal State ===

    UPROPERTY()
    TObjectPtr<UAuracronMetaHumanFramework> OwnerFramework;

    UPROPERTY()
    bool bIsInitialized = false;

    UPROPERTY()
    FString LastErrorMessage;

#if WITH_METAHUMAN_DNA_CALIBRATION
    // DNA Reader and Writer instances
    TUniquePtr<dna::BinaryStreamReader> DNAReader;
    TUniquePtr<dna::BinaryStreamWriter> DNAWriter;
    TUniquePtr<dnacalib::DNACalibDNAReader> DNACalibration;
#endif

    // Async operation tracking
    TMap<FString, TSharedPtr<class FAuracronDNAAsyncTask>> ActiveAsyncOperations;
    FCriticalSection AsyncOperationsMutex;

    // MetaHuman DNA Runtime instance
    TSharedPtr<class FAuracronMetaHumanDNARuntime> MetaHumanDNARuntime;

private:
    // === Internal Methods ===

    bool ValidateDNAReader() const;
    bool ValidateDNAWriter() const;
    FString GenerateOperationID() const;
    void CleanupCompletedAsyncOperations();

    // MetaHuman DNA Runtime functions
    bool InitializeMetaHumanDNARuntime();
    bool ParseDNAData(const TArray<uint8>& DNAData);
    bool ValidateDNAStructure() const;
    FAuracronDNAMetadata ExtractDNAMetadata(const TArray<uint8>& DNAData) const;
    bool SerializeDNAData(TArray<uint8>& OutDNAData) const;

#if WITH_METAHUMAN_DNA_CALIBRATION
    bool InitializeDNALibrary();
    void ShutdownDNALibrary();
    bool LoadDNAInternal(const FString& FilePath);
    bool SaveDNAInternal(const FString& FilePath);
#endif
};

