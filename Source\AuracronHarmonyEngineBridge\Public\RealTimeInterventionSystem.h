#pragma once

#include "CoreMinimal.h"
#include "UObject/NoExportTypes.h"
#include "GameplayTagContainer.h"
#include "Engine/DataTable.h"
#include "AuracronHarmonyEngineBridge.h"
#include "RealTimeInterventionSystem.generated.h"

class APlayerController;
class UAbilitySystemComponent;

UENUM(BlueprintType)
enum class EInterventionMethod : uint8
{
    InGameMessage       UMETA(DisplayName = "In-Game Message"),
    UINotification      UMETA(DisplayName = "UI Notification"),
    AudioCue            UMETA(DisplayName = "Audio Cue"),
    VisualEffect        UMETA(DisplayName = "Visual Effect"),
    GameplayModifier    UMETA(DisplayName = "Gameplay Modifier"),
    PeerConnection      UMETA(DisplayName = "Peer Connection"),
    MentorAssignment    UMETA(DisplayName = "Mentor Assignment"),
    BreakSuggestion     UMETA(DisplayName = "Break Suggestion")
};

UENUM(BlueprintType)
enum class EInterventionResult : uint8
{
    Pending             UMETA(DisplayName = "Pending"),
    Accepted            UMETA(DisplayName = "Accepted"),
    Declined            UMETA(DisplayName = "Declined"),
    Ignored             UMETA(DisplayName = "Ignored"),
    Successful          UMETA(DisplayName = "Successful"),
    Failed              UMETA(DisplayName = "Failed"),
    Escalated           UMETA(DisplayName = "Escalated")
};

USTRUCT(BlueprintType)
struct AURACRONHARMONYENGINEBRIDGE_API FActiveIntervention
{
    GENERATED_BODY()

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    FString InterventionID;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    FString PlayerID;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    EInterventionType InterventionType;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    EInterventionMethod Method;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    EInterventionResult Result;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    FString InterventionMessage;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    FDateTime StartTime;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    FDateTime ResponseTime;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    float EffectivenessScore;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    bool bRequiresFollowUp;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    FGameplayTagContainer InterventionTags;

    FActiveIntervention()
    {
        InterventionID = FGuid::NewGuid().ToString();
        PlayerID = TEXT("");
        InterventionType = EInterventionType::None;
        Method = EInterventionMethod::InGameMessage;
        Result = EInterventionResult::Pending;
        InterventionMessage = TEXT("");
        StartTime = FDateTime::Now();
        ResponseTime = FDateTime::MinValue();
        EffectivenessScore = 0.0f;
        bRequiresFollowUp = false;
    }
};

USTRUCT(BlueprintType)
struct AURACRONHARMONYENGINEBRIDGE_API FInterventionStrategy
{
    GENERATED_BODY()

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    EInterventionType TriggerType;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    TArray<EInterventionMethod> PreferredMethods;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    float SuccessRate;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    float CooldownTime;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    int32 MaxAttemptsPerSession;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    FGameplayTagContainer StrategyTags;

    FInterventionStrategy()
    {
        TriggerType = EInterventionType::None;
        SuccessRate = 0.0f;
        CooldownTime = 300.0f;
        MaxAttemptsPerSession = 3;
    }
};

/**
 * Real-Time Intervention System
 * Provides immediate, contextual interventions to prevent toxicity and promote positive behavior
 */
UCLASS(BlueprintType, Blueprintable)
class AURACRONHARMONYENGINEBRIDGE_API URealTimeInterventionSystem : public UObject
{
    GENERATED_BODY()

public:
    URealTimeInterventionSystem();

    // Core Intervention Functions
    UFUNCTION(BlueprintCallable, Category = "Real-Time Intervention")
    FString ExecuteIntervention(const FString& PlayerID, const FHarmonyInterventionData& InterventionData);

    UFUNCTION(BlueprintCallable, Category = "Real-Time Intervention")
    void RespondToIntervention(const FString& InterventionID, EInterventionResult Response);

    UFUNCTION(BlueprintCallable, Category = "Real-Time Intervention")
    void EscalateIntervention(const FString& InterventionID);

    UFUNCTION(BlueprintCallable, Category = "Real-Time Intervention")
    void CompleteIntervention(const FString& InterventionID, float EffectivenessScore);

    // Intervention Management
    UFUNCTION(BlueprintCallable, Category = "Real-Time Intervention")
    TArray<FActiveIntervention> GetActiveInterventions();

    UFUNCTION(BlueprintCallable, Category = "Real-Time Intervention")
    FActiveIntervention GetIntervention(const FString& InterventionID);

    UFUNCTION(BlueprintCallable, Category = "Real-Time Intervention")
    bool IsPlayerUnderIntervention(const FString& PlayerID);

    UFUNCTION(BlueprintCallable, Category = "Real-Time Intervention")
    void CancelIntervention(const FString& InterventionID, const FString& Reason);

    UFUNCTION(BlueprintCallable, Category = "Real-Time Intervention")
    void ExecuteFollowUpIntervention(const FString& OriginalInterventionID);

    // Strategy Management
    UFUNCTION(BlueprintCallable, Category = "Real-Time Intervention")
    void RegisterInterventionStrategy(const FInterventionStrategy& Strategy);

    UFUNCTION(BlueprintCallable, Category = "Real-Time Intervention")
    EInterventionMethod SelectOptimalMethod(const FString& PlayerID, EInterventionType InterventionType);

    UFUNCTION(BlueprintCallable, Category = "Real-Time Intervention")
    float GetStrategyEffectiveness(EInterventionType InterventionType, EInterventionMethod Method);

    // Analytics
    UFUNCTION(BlueprintCallable, Category = "Real-Time Intervention")
    float GetOverallInterventionSuccessRate();

    UFUNCTION(BlueprintCallable, Category = "Real-Time Intervention")
    int32 GetTotalInterventionsToday();

    UFUNCTION(BlueprintCallable, Category = "Real-Time Intervention")
    TMap<EInterventionType, int32> GetInterventionTypeStatistics();

protected:
    // Configuration
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Intervention Config")
    int32 MaxConcurrentInterventions;

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Intervention Config")
    float InterventionTimeout;

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Intervention Config")
    float EscalationThreshold;

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Intervention Config")
    bool bEnableAutomaticEscalation;

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Intervention Config")
    bool bEnableInterventionLearning;

    // Data Storage
    UPROPERTY()
    TMap<FString, FActiveIntervention> ActiveInterventions;

    UPROPERTY()
    TArray<FActiveIntervention> CompletedInterventions;

    UPROPERTY()
    TMap<EInterventionType, FInterventionStrategy> InterventionStrategies;

    UPROPERTY()
    TMap<FString, int32> PlayerInterventionCounts;

    // Timers
    UPROPERTY()
    TMap<FString, FTimerHandle> InterventionTimers;

    // Special Event State
    UPROPERTY()
    bool bSpecialEventActive;

    UPROPERTY()
    float CurrentEventMultiplier;

private:
    // Initialization
    void InitializeDefaultStrategies();
    
    // Crisis and Emergency handling
    void TriggerCrisisProtocol(const FString& PlayerID);
    FString GenerateEscalatedMessage(EInterventionType NewType, const FString& PlayerID);
    void NotifyCommunityModerators(const FString& PlayerID);
    void LogCrisisEvent(const FString& PlayerID);
    
    // Ability System Effects
    void ApplyCalminEffect(UAbilitySystemComponent* ASC);
    void ApplyFocusEnhancement(UAbilitySystemComponent* ASC);
    void ApplyStressReduction(UAbilitySystemComponent* ASC);
    void ApplyEmergencySupport(UAbilitySystemComponent* ASC);
    
    // Utility
    int32 CalculateResponseBonus(EInterventionType InterventionType);
    // Intervention execution
    void DeliverInterventionMessage(const FString& PlayerID, const FString& Message, EInterventionMethod Method);
    void ApplyInterventionEffects(const FString& PlayerID, const FActiveIntervention& Intervention);
    void MonitorInterventionResponse(const FString& InterventionID);
    
    // Method-specific delivery
    void DeliverInGameMessage(const FString& PlayerID, const FString& Message);
    void DeliverUINotification(const FString& PlayerID, const FString& Message);
    void DeliverAudioCue(const FString& PlayerID, const FString& Message);
    void DeliverVisualEffect(const FString& PlayerID, const FString& Message);
    void ApplyGameplayModifier(const FString& PlayerID, const FActiveIntervention& Intervention);
    void InitiatePeerConnection(const FString& PlayerID);
    void AssignMentor(const FString& PlayerID);
    void SuggestBreak(const FString& PlayerID);
    
    // Response handling
    void ProcessInterventionResponse(const FString& InterventionID, EInterventionResult Response);
    void HandleSuccessfulIntervention(const FString& InterventionID);
    void HandleFailedIntervention(const FString& InterventionID);
    void ScheduleFollowUp(const FString& InterventionID);
    
    // Learning and adaptation
    void UpdateStrategyEffectiveness(EInterventionType Type, EInterventionMethod Method, bool bSuccessful);
    void AdaptInterventionStrategies();
    void AnalyzeInterventionPatterns();
    
    // Validation and safety
    bool ValidateInterventionRequest(const FString& PlayerID, const FHarmonyInterventionData& InterventionData);
    bool IsInterventionAppropriate(const FString& PlayerID, EInterventionType InterventionType);
    void EnsurePlayerSafety(const FString& PlayerID);
    
    // Utility functions
    FString GenerateInterventionID();
    void StartInterventionTimer(const FString& InterventionID);
    void OnInterventionTimeout(const FString& InterventionID);
    UAbilitySystemComponent* GetPlayerAbilitySystemComponent(const FString& PlayerID);
    APlayerController* GetPlayerController(const FString& PlayerID);
};
